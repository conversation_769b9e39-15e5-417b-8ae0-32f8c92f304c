// WebSocketContext - Singleton WebSocket para evitar múltiplas conexões

import React, { createContext, useContext, useEffect, useRef, useState, useCallback } from 'react'
import type { ArbitrageOpportunity } from '@/types/arbitrage'

interface WebSocketMessage {
  type: 'welcome' | 'opportunity' | 'opportunities' | 'heartbeat' | 'stats' | 'pong'
  data?: any
  timestamp: number
  count?: number
}

interface WebSocketContextType {
  isConnected: boolean
  isConnecting: boolean
  lastMessage: WebSocketMessage | null
  sendMessage: (message: any) => void
  stats: {
    messagesReceived: number
    messagesSent: number
    averageLatency: number
    uptime: number
  }
}

const WebSocketContext = createContext<WebSocketContextType | null>(null)

interface WebSocketProviderProps {
  children: React.ReactNode
  url?: string
}

export function WebSocketProvider({
  children,
  url = 'ws://localhost:3001/ws'
}: WebSocketProviderProps) {
  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null)
  const [stats, setStats] = useState({
    messagesReceived: 0,
    messagesSent: 0,
    averageLatency: 0,
    uptime: 0
  })

  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const startTimeRef = useRef<number>(Date.now())
  const latencyMeasurements = useRef<number[]>([])
  const lastPingTime = useRef<number>(0)

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      console.log('🔌 WebSocket already connected')
      return
    }

    if (isConnecting) {
      console.log('🔌 WebSocket already connecting')
      return
    }

    try {
      console.log('🚀 SINGLETON: Connecting to WebSocket:', url)
      setIsConnecting(true)
      
      const ws = new WebSocket(url)
      wsRef.current = ws

      ws.onopen = () => {
        console.log('🎉 CRÍTICO: WebSocket conectado com sucesso!', url)
        setIsConnected(true)
        setIsConnecting(false)
        startTimeRef.current = Date.now()

        // 🚀 CRÍTICO: Enviar mensagem de teste imediatamente
        ws.send(JSON.stringify({
          type: 'client_connected',
          timestamp: Date.now(),
          userAgent: navigator.userAgent
        }))
      }

      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          
          console.log('🎉 SINGLETON: Message received!', { 
            type: message.type, 
            hasData: !!message.data,
            dataLength: Array.isArray(message.data) ? message.data.length : 'not array'
          })

          setLastMessage(message)
          setStats(prev => ({
            ...prev,
            messagesReceived: prev.messagesReceived + 1,
            uptime: Date.now() - startTimeRef.current
          }))

          // Handle pong for latency calculation
          if (message.type === 'pong' && lastPingTime.current > 0) {
            const latency = Date.now() - lastPingTime.current
            latencyMeasurements.current.push(latency)
            
            if (latencyMeasurements.current.length > 10) {
              latencyMeasurements.current = latencyMeasurements.current.slice(-10)
            }
            
            const avgLatency = latencyMeasurements.current.reduce((sum, l) => sum + l, 0) / latencyMeasurements.current.length
            setStats(prev => ({ ...prev, averageLatency: avgLatency }))
          }

          // 🚀 VISUAL DEBUG: Update page title for opportunities
          if (message.type === 'opportunities' && message.data && Array.isArray(message.data)) {
            const opportunities: ArbitrageOpportunity[] = message.data
            if (opportunities.length > 0) {
              document.title = `🎉 SINGLETON: ${opportunities.length} ops - ${opportunities[0].symbol} ${opportunities[0].spreadPercentage.toFixed(2)}%`
            }
          }

        } catch (error) {
          console.error('❌ SINGLETON: Error parsing message:', error)
        }
      }

      ws.onclose = () => {
        console.log('🔌 SINGLETON: WebSocket disconnected')
        setIsConnected(false)
        setIsConnecting(false)
        
        // 🚀 OTIMIZADO: Reconnect inteligente com backoff exponencial
        const reconnectAttempts = stats.messagesSent % 10 // Use messagesSent as attempt counter
        const reconnectDelay = Math.min(1000 * Math.pow(1.5, reconnectAttempts), 15000) // Max 15s

        console.log(`🔄 SINGLETON: Attempting to reconnect in ${reconnectDelay/1000}s... (attempt ${reconnectAttempts + 1})`)
        reconnectTimeoutRef.current = setTimeout(() => {
          console.log('🔄 SINGLETON: Attempting to reconnect...')
          connect()
        }, reconnectDelay)
      }

      ws.onerror = (error) => {
        console.error('❌ SINGLETON: WebSocket error:', error)
        setIsConnecting(false)
      }

    } catch (error) {
      console.error('❌ SINGLETON: Error creating WebSocket:', error)
      setIsConnecting(false)
    }
  }, [url, isConnecting])

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      try {
        const messageStr = JSON.stringify(message)
        wsRef.current.send(messageStr)
        
        setStats(prev => ({ ...prev, messagesSent: prev.messagesSent + 1 }))
        
        if (message.type === 'ping') {
          lastPingTime.current = Date.now()
        }
        
      } catch (error) {
        console.error('❌ SINGLETON: Error sending message:', error)
      }
    }
  }, [])

  // 🚀 CRÍTICO: Connect agressivo on mount
  useEffect(() => {
    console.log('🚀 WebSocket: Iniciando conexão agressiva...')
    connect()

    // 🚀 CRÍTICO: Tentar reconectar a cada 3s se não conectado
    const forceReconnectInterval = setInterval(() => {
      if (!isConnected && !isConnecting) {
        console.log('🔄 WebSocket: Forçando reconexão...')
        connect()
      }
    }, 3000)

    // Cleanup on unmount
    return () => {
      clearInterval(forceReconnectInterval)
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
      if (wsRef.current) {
        wsRef.current.close()
      }
    }
  }, [connect, isConnected, isConnecting])

  // Heartbeat
  useEffect(() => {
    if (!isConnected) return

    const heartbeatInterval = setInterval(() => {
      sendMessage({ type: 'ping', timestamp: Date.now() })
    }, 30000) // 30 seconds

    return () => clearInterval(heartbeatInterval)
  }, [isConnected, sendMessage])

  const value: WebSocketContextType = {
    isConnected,
    isConnecting,
    lastMessage,
    sendMessage,
    stats
  }

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  )
}

export function useWebSocketContext() {
  const context = useContext(WebSocketContext)
  if (!context) {
    throw new Error('useWebSocketContext must be used within a WebSocketProvider')
  }
  return context
}
