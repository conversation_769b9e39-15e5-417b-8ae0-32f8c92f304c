<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Teste Completo do Sistema</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #1a4a1a; color: #00ff00; }
        .error { background: #4a1a1a; color: #ff0000; }
        .warning { background: #4a4a1a; color: #ffff00; }
        .info { background: #1a1a4a; color: #00aaff; }
        .log {
            background: #000;
            border: 1px solid #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-size: 12px;
            line-height: 1.4;
        }
        button {
            background: #333;
            color: #00ff00;
            border: 1px solid #555;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
        }
        button:hover { background: #444; }
        button:disabled { opacity: 0.5; cursor: not-allowed; }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric {
            background: #333;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #00ff00;
        }
        .metric-label {
            font-size: 12px;
            color: #aaa;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 TESTE COMPLETO DO SISTEMA SPOT-FUTUROS V3</h1>
        
        <div class="section">
            <h2>📊 Status dos Serviços</h2>
            <div id="backend-status" class="status info">🔄 Verificando Backend...</div>
            <div id="frontend-status" class="status info">🔄 Verificando Frontend...</div>
            <div id="websocket-status" class="status info">🔄 Verificando WebSocket...</div>
        </div>

        <div class="section">
            <h2>🎛️ Controles de Teste</h2>
            <button onclick="testBackend()">🔧 Testar Backend</button>
            <button onclick="testFrontend()">🖥️ Testar Frontend</button>
            <button onclick="testWebSocket()">📡 Testar WebSocket</button>
            <button onclick="testComplete()">🚀 Teste Completo</button>
            <button onclick="clearLogs()">🧹 Limpar Logs</button>
        </div>

        <div class="section">
            <h2>📈 Métricas em Tempo Real</h2>
            <div class="metrics">
                <div class="metric">
                    <div class="metric-value" id="opportunities-count">0</div>
                    <div class="metric-label">Oportunidades</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="websocket-messages">0</div>
                    <div class="metric-label">Mensagens WS</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="latency">0ms</div>
                    <div class="metric-label">Latência</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="connection-status">❌</div>
                    <div class="metric-label">Conexão</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📝 Logs do Sistema</h2>
            <div id="logs" class="log">
                <div>🚀 Sistema de teste iniciado...</div>
            </div>
        </div>

        <div class="section">
            <h2>🔍 Dados Recebidos</h2>
            <div id="data-display" class="log">
                <div>⏳ Aguardando dados...</div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let messageCount = 0;
        let opportunitiesCount = 0;
        let lastPingTime = 0;

        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            logs.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function updateMetric(metricId, value) {
            document.getElementById(metricId).textContent = value;
        }

        async function testBackend() {
            log('🔧 Testando Backend...', 'info');
            
            try {
                // Teste de health check
                const healthResponse = await fetch('http://localhost:3001/health');
                if (healthResponse.ok) {
                    const healthData = await healthResponse.json();
                    log(`✅ Backend Health: ${JSON.stringify(healthData)}`, 'success');
                    updateStatus('backend-status', '✅ Backend Online', 'success');
                } else {
                    throw new Error(`Health check failed: ${healthResponse.status}`);
                }

                // Teste de API de oportunidades
                const oppResponse = await fetch('http://localhost:3001/api/opportunities');
                if (oppResponse.ok) {
                    const oppData = await oppResponse.json();
                    log(`✅ API Oportunidades: ${oppData.opportunities?.length || 0} oportunidades`, 'success');
                    opportunitiesCount = oppData.opportunities?.length || 0;
                    updateMetric('opportunities-count', opportunitiesCount);
                } else {
                    throw new Error(`API opportunities failed: ${oppResponse.status}`);
                }

            } catch (error) {
                log(`❌ Erro no Backend: ${error.message}`, 'error');
                updateStatus('backend-status', '❌ Backend Offline', 'error');
            }
        }

        async function testFrontend() {
            log('🖥️ Testando Frontend...', 'info');
            
            try {
                const response = await fetch('http://localhost:5002');
                if (response.ok) {
                    log('✅ Frontend acessível', 'success');
                    updateStatus('frontend-status', '✅ Frontend Online', 'success');
                } else {
                    throw new Error(`Frontend not accessible: ${response.status}`);
                }
            } catch (error) {
                log(`❌ Erro no Frontend: ${error.message}`, 'error');
                updateStatus('frontend-status', '❌ Frontend Offline', 'error');
            }
        }

        function testWebSocket() {
            log('📡 Testando WebSocket...', 'info');
            
            if (ws) {
                ws.close();
            }

            ws = new WebSocket('ws://localhost:3001/ws');
            
            ws.onopen = function() {
                log('✅ WebSocket conectado!', 'success');
                updateStatus('websocket-status', '✅ WebSocket Conectado', 'success');
                updateMetric('connection-status', '✅');
                
                // Enviar ping para testar latência
                lastPingTime = Date.now();
                ws.send(JSON.stringify({ type: 'ping', timestamp: lastPingTime }));
            };

            ws.onmessage = function(event) {
                messageCount++;
                updateMetric('websocket-messages', messageCount);
                
                try {
                    const data = JSON.parse(event.data);
                    
                    if (data.type === 'pong') {
                        const latency = Date.now() - lastPingTime;
                        updateMetric('latency', `${latency}ms`);
                        log(`📊 Latência: ${latency}ms`, 'info');
                    } else if (data.type === 'opportunities') {
                        const count = data.data?.length || 0;
                        opportunitiesCount = count;
                        updateMetric('opportunities-count', count);
                        log(`📈 Recebidas ${count} oportunidades via WebSocket`, 'success');
                        
                        // Mostrar primeiras 3 oportunidades
                        if (data.data && data.data.length > 0) {
                            const display = document.getElementById('data-display');
                            const sample = data.data.slice(0, 3).map(opp => 
                                `${opp.symbol}: ${opp.spreadPercentage?.toFixed(2)}%`
                            ).join(', ');
                            display.innerHTML = `<div>📊 Últimas oportunidades: ${sample}</div>`;
                        }
                    } else {
                        log(`📨 Mensagem WebSocket: ${data.type}`, 'info');
                    }
                } catch (error) {
                    log(`❌ Erro ao processar mensagem WebSocket: ${error.message}`, 'error');
                }
            };

            ws.onerror = function(error) {
                log(`❌ Erro WebSocket: ${error}`, 'error');
                updateStatus('websocket-status', '❌ WebSocket Erro', 'error');
                updateMetric('connection-status', '❌');
            };

            ws.onclose = function() {
                log('🔌 WebSocket desconectado', 'warning');
                updateStatus('websocket-status', '🔌 WebSocket Desconectado', 'warning');
                updateMetric('connection-status', '🔌');
            };
        }

        async function testComplete() {
            log('🚀 Iniciando teste completo...', 'info');
            await testBackend();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testFrontend();
            await new Promise(resolve => setTimeout(resolve, 1000));
            testWebSocket();
            log('🎉 Teste completo finalizado!', 'success');
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '<div>🧹 Logs limpos...</div>';
            document.getElementById('data-display').innerHTML = '<div>⏳ Aguardando dados...</div>';
        }

        // Auto-iniciar teste completo
        window.onload = function() {
            setTimeout(testComplete, 1000);
        };

        // Ping periódico para medir latência
        setInterval(() => {
            if (ws && ws.readyState === WebSocket.OPEN) {
                lastPingTime = Date.now();
                ws.send(JSON.stringify({ type: 'ping', timestamp: lastPingTime }));
            }
        }, 5000);
    </script>
</body>
</html>
