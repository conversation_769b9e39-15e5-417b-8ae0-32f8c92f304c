#!/usr/bin/env node

/**
 * 🧪 TESTE DAS OTIMIZAÇÕES IMPLEMENTADAS
 * Verificar se conseguimos reduzir de 4200ms para <1000ms
 */

import fetch from 'node-fetch';

const BACKEND_URL = 'http://localhost:3001';

console.log('🧪 TESTANDO OTIMIZAÇÕES DO BACKEND');
console.log('=' .repeat(50));
console.log('Target: Reduzir de 4200ms para <1000ms');
console.log('');

async function testOptimizations() {
  console.log('📊 Testando performance após otimizações...\n');
  
  const results = [];
  
  // Teste 1: Primeira chamada (sem cache)
  console.log('🔍 Teste 1: Primeira chamada (sem cache)');
  const start1 = Date.now();
  
  try {
    const response1 = await fetch(`${BACKEND_URL}/api/arbitrage/opportunities`);
    const time1 = Date.now() - start1;
    
    if (response1.ok) {
      const data1 = await response1.json();
      results.push({
        test: 'Primeira chamada',
        totalTime: time1,
        processingTime: data1.processingTime,
        opportunities: data1.count,
        cached: false
      });
      
      console.log(`   ⏱️ Tempo total: ${time1}ms`);
      console.log(`   ⚙️ Processing: ${data1.processingTime}ms`);
      console.log(`   📊 Oportunidades: ${data1.count}`);
      console.log(`   🎯 Target: ${time1 <= 1000 ? '✅ ATINGIDO' : '❌ NÃO ATINGIDO'}`);
    }
  } catch (error) {
    console.log(`   ❌ Erro: ${error.message}`);
  }
  
  console.log('');
  
  // Aguardar 500ms
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Teste 2: Segunda chamada (com cache)
  console.log('🔍 Teste 2: Segunda chamada (com cache)');
  const start2 = Date.now();
  
  try {
    const response2 = await fetch(`${BACKEND_URL}/api/arbitrage/opportunities`);
    const time2 = Date.now() - start2;
    
    if (response2.ok) {
      const data2 = await response2.json();
      results.push({
        test: 'Segunda chamada',
        totalTime: time2,
        processingTime: data2.processingTime,
        opportunities: data2.count,
        cached: time2 < 100 // Provavelmente cache se < 100ms
      });
      
      console.log(`   ⏱️ Tempo total: ${time2}ms`);
      console.log(`   ⚙️ Processing: ${data2.processingTime}ms`);
      console.log(`   📊 Oportunidades: ${data2.count}`);
      console.log(`   💾 Cache: ${time2 < 100 ? '✅ HIT' : '❌ MISS'}`);
      console.log(`   🎯 Target: ${time2 <= 1000 ? '✅ ATINGIDO' : '❌ NÃO ATINGIDO'}`);
    }
  } catch (error) {
    console.log(`   ❌ Erro: ${error.message}`);
  }
  
  console.log('');
  
  // Aguardar 3s para cache expirar
  console.log('⏳ Aguardando 3s para cache expirar...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Teste 3: Terceira chamada (cache expirado)
  console.log('🔍 Teste 3: Terceira chamada (cache expirado)');
  const start3 = Date.now();
  
  try {
    const response3 = await fetch(`${BACKEND_URL}/api/arbitrage/opportunities`);
    const time3 = Date.now() - start3;
    
    if (response3.ok) {
      const data3 = await response3.json();
      results.push({
        test: 'Terceira chamada',
        totalTime: time3,
        processingTime: data3.processingTime,
        opportunities: data3.count,
        cached: false
      });
      
      console.log(`   ⏱️ Tempo total: ${time3}ms`);
      console.log(`   ⚙️ Processing: ${data3.processingTime}ms`);
      console.log(`   📊 Oportunidades: ${data3.count}`);
      console.log(`   🎯 Target: ${time3 <= 1000 ? '✅ ATINGIDO' : '❌ NÃO ATINGIDO'}`);
    }
  } catch (error) {
    console.log(`   ❌ Erro: ${error.message}`);
  }
  
  console.log('');
  
  // Análise dos resultados
  console.log('📈 ANÁLISE DOS RESULTADOS');
  console.log('-' .repeat(30));
  
  const nonCachedResults = results.filter(r => !r.cached);
  if (nonCachedResults.length > 0) {
    const avgTime = nonCachedResults.reduce((sum, r) => sum + r.totalTime, 0) / nonCachedResults.length;
    const avgProcessing = nonCachedResults.reduce((sum, r) => sum + r.processingTime, 0) / nonCachedResults.length;
    const avgOpportunities = nonCachedResults.reduce((sum, r) => sum + r.opportunities, 0) / nonCachedResults.length;
    
    console.log(`📊 Tempo médio (sem cache): ${Math.round(avgTime)}ms`);
    console.log(`📊 Processing médio: ${Math.round(avgProcessing)}ms`);
    console.log(`📊 Oportunidades médias: ${Math.round(avgOpportunities)}`);
    
    // Comparação com baseline
    const baseline = 4200; // Tempo anterior
    const improvement = ((baseline - avgTime) / baseline) * 100;
    
    console.log(`📊 Baseline anterior: ${baseline}ms`);
    console.log(`📊 Melhoria: ${improvement.toFixed(1)}%`);
    
    if (avgTime <= 1000) {
      console.log(`🎉 SUCESSO: Target de <1000ms ATINGIDO!`);
    } else {
      console.log(`⚠️ PARCIAL: Ainda ${avgTime - 1000}ms acima do target`);
    }
    
    // Verificar cache effectiveness
    const cachedResults = results.filter(r => r.cached);
    if (cachedResults.length > 0) {
      const avgCacheTime = cachedResults.reduce((sum, r) => sum + r.totalTime, 0) / cachedResults.length;
      console.log(`💾 Cache médio: ${Math.round(avgCacheTime)}ms`);
      console.log(`💾 Cache effectiveness: ${avgCacheTime < 100 ? '✅ EXCELENTE' : '⚠️ PODE MELHORAR'}`);
    }
  }
  
  // Verificar cache stats
  try {
    const cacheResponse = await fetch(`${BACKEND_URL}/api/cache/stats`);
    if (cacheResponse.ok) {
      const cacheData = await cacheResponse.json();
      console.log(`💾 Hit Rate atual: ${cacheData.data?.hitRate || 0}%`);
      console.log(`💾 Total Entries: ${cacheData.data?.totalEntries || 0}`);
    }
  } catch (error) {
    console.log(`❌ Erro ao obter cache stats: ${error.message}`);
  }
  
  console.log('\n🏁 Teste de otimizações concluído');
  
  return results;
}

// Executar teste
testOptimizations()
  .then((results) => {
    const success = results.some(r => !r.cached && r.totalTime <= 1000);
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('💥 Erro no teste:', error);
    process.exit(1);
  });