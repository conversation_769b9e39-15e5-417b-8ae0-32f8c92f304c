// useArbitrageData - Hook Ultra-Otimizado com WebSocket Streaming

import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect, useRef, useCallback, useState } from 'react'
import { DataCollector } from '@/services/DataCollector'
import { AlertSystem } from '@/services/AlertSystem'
import { useWebSocketContext } from '@/contexts/WebSocketContext'
import type { ArbitrageOpportunity, DashboardMetrics } from '@/types/arbitrage'

interface UseArbitrageDataOptions {
  enabled?: boolean
  refetchInterval?: number
  staleTime?: number
  cacheTime?: number
  enableAlerts?: boolean
  enableRealTime?: boolean
}

interface UseArbitrageDataReturn {
  opportunities: ArbitrageOpportunity[]
  metrics: DashboardMetrics | null
  isLoading: boolean
  isError: boolean
  error: Error | null
  isRefetching: boolean
  refetch: () => void
  lastUpdate: Date | null
  dataAge: number
  connectionStatus: 'connected' | 'connecting' | 'disconnected'
  performanceMetrics: {
    responseTime: number
    cacheHitRate: number
    errorRate: number
  }
  // 🚀 ULTRA-WEBSOCKET: New WebSocket-related properties
  websocket: {
    connectionState: 'connecting' | 'connected' | 'disconnected' | 'reconnecting' | 'error'
    messagesReceived: number
    averageLatency: number
    uptime: number
    realTimeUpdates: number
  }
}

const DEFAULT_OPTIONS: UseArbitrageDataOptions = {
  enabled: true,
  refetchInterval: 1000, // 1s ultra-fast
  staleTime: 100, // 🚀 ULTRA-AGGRESSIVE: 100ms for freshest data
  cacheTime: 1000, // 🚀 ULTRA-FAST: 1s cache for maximum speed
  enableAlerts: true,
  enableRealTime: true
}

export function useArbitrageData(
  options: UseArbitrageDataOptions = {}
): UseArbitrageDataReturn {
  console.log('🚀 HOOK: useArbitrageData called with options:', options)
  const opts = { ...DEFAULT_OPTIONS, ...options }
  const queryClient = useQueryClient()
  
  // 🚀 ULTRA-WEBSOCKET: Use singleton WebSocket context
  const {
    isConnected: wsIsConnected,
    isConnecting: wsIsConnecting,
    lastMessage: wsLastMessage,
    sendMessage: wsSendMessage,
    stats: wsStats
  } = useWebSocketContext()

  // Map to expected interface
  const wsConnectionState = wsIsConnected ? 'connected' : wsIsConnecting ? 'connecting' : 'disconnected'
  
  // 🚀 ULTRA-WEBSOCKET: Real-time update tracking
  const [realTimeUpdates, setRealTimeUpdates] = useState(0)
  const lastWebSocketUpdate = useRef<Date | null>(null)
  
  // Instâncias dos services
  const dataCollector = DataCollector.getInstance()
  const alertSystem = AlertSystem.getInstance()

  // 🚀 ULTRA-WEBSOCKET: Process WebSocket messages for real-time updates
  useEffect(() => {
    console.log('🔍 WS Effect triggered:', {
      hasMessage: !!wsLastMessage,
      messageType: wsLastMessage?.type,
      enableRealTime: opts.enableRealTime,
      connectionState: wsConnectionState,
      timestamp: wsLastMessage?.timestamp
    })

    if (!wsLastMessage || !opts.enableRealTime) {
      console.log('🔍 WS: Saindo - sem mensagem ou tempo real desabilitado')
      return
    }

    // 🚀 OTIMIZADO: Processar mensagens de oportunidades
    if (!['opportunities', 'opportunity', 'welcome'].includes(wsLastMessage.type)) {
      console.log('🔍 WS: Ignorando mensagem tipo:', wsLastMessage.type)
      return
    }

    console.log('🔍 WS Processing:', {
      messageType: wsLastMessage.type,
      dataLength: Array.isArray(wsLastMessage.data) ? wsLastMessage.data.length : 1,
      connectionState: wsConnectionState,
      data: wsLastMessage.data
    })

    const processWebSocketMessage = async () => {
      const startTime = performance.now()

      try {
        if (wsLastMessage.type === 'opportunity' && wsLastMessage.data) {
          const opportunity: ArbitrageOpportunity = wsLastMessage.data

          console.log(`⚡ ULTRA-WS: Real-time opportunity received: ${opportunity.symbol}`)

          // 🚀 ULTRA-FAST: Update React Query cache with new opportunity
          queryClient.setQueryData(['arbitrage-data'], (oldData: any) => {
            if (!oldData) return oldData

            const existingOpportunities = oldData.opportunities || []
            
            // Check if opportunity already exists (avoid duplicates)
            const existingIndex = existingOpportunities.findIndex(
              (opp: ArbitrageOpportunity) => opp.id === opportunity.id
            )

            let updatedOpportunities: ArbitrageOpportunity[]
            
            if (existingIndex >= 0) {
              // Update existing opportunity
              updatedOpportunities = [...existingOpportunities]
              updatedOpportunities[existingIndex] = opportunity
            } else {
              // Add new opportunity at the beginning
              updatedOpportunities = [opportunity, ...existingOpportunities]
            }

            // Keep only the most recent 1000 opportunities for performance
            if (updatedOpportunities.length > 1000) {
              updatedOpportunities = updatedOpportunities.slice(0, 1000)
            }

            // Sort by spread percentage (descending)
            updatedOpportunities.sort((a, b) => 
              Math.abs(b.spreadPercentage) - Math.abs(a.spreadPercentage)
            )

            // Update metrics
            const updatedMetrics = dataCollector.calculateDashboardMetrics(updatedOpportunities)

            return {
              opportunities: updatedOpportunities,
              metrics: updatedMetrics
            }
          })

          // Update real-time stats
          setRealTimeUpdates(prev => prev + 1)
          lastWebSocketUpdate.current = new Date()

          // Check for alerts
          if (opts.enableAlerts) {
            alertSystem.checkCrossExchangeSpreadAlert(opportunity)
          }

          const processingTime = performance.now() - startTime
          
          // 🚨 ULTRA-AGGRESSIVE ALERT: Target <16ms for real-time updates
          if (processingTime > 16) {
            console.warn(`🚨 ULTRA-WS: Slow real-time update: ${processingTime.toFixed(1)}ms (target: <16ms)`)
          } else {
            console.log(`⚡ ULTRA-WS: Real-time update processed in ${processingTime.toFixed(1)}ms`)
          }
        } else if (wsLastMessage.type === 'opportunities' && wsLastMessage.data) {
          // 🚀 ULTRA-BATCH: Handle multiple opportunities at once
          const opportunities: ArbitrageOpportunity[] = wsLastMessage.data

          console.log(`🎉 FRONTEND: Batch of ${opportunities.length} opportunities received via WebSocket!`)

          // 🚀 VISUAL DEBUG: Update page title to show WebSocket is working
          if (opportunities.length > 0) {
            document.title = `🎉 WS: ${opportunities.length} ops - ${opportunities[0].symbol} ${opportunities[0].spreadPercentage.toFixed(2)}%`
            console.log(`🚀 FRONTEND: First opportunity: ${opportunities[0].symbol} - ${opportunities[0].spreadPercentage.toFixed(2)}%`)
          }

          // 🚀 ULTRA-FAST: Replace entire opportunities array for better performance
          queryClient.setQueryData(['arbitrage-data'], (oldData: any) => {
            if (!oldData) return oldData

            // 🚀 OTIMIZADO: Filtrar e ordenar apenas oportunidades relevantes
            const relevantOpportunities = opportunities
              .filter(opp => Math.abs(opp.spreadPercentage) > 0.05) // Filtrar spreads > 0.05%
              .slice(0, 500) // 🚀 OTIMIZADO: Limitar a 500 para performance
              .sort((a, b) => Math.abs(b.spreadPercentage) - Math.abs(a.spreadPercentage))

            // Update metrics
            const updatedMetrics = dataCollector.calculateDashboardMetrics(relevantOpportunities)

            return {
              opportunities: relevantOpportunities,
              metrics: updatedMetrics
            }
          })

          // Update real-time stats
          setRealTimeUpdates(prev => prev + opportunities.length)
          lastWebSocketUpdate.current = new Date()

          const processingTime = performance.now() - startTime

          if (processingTime > 16) {
            console.warn(`🚨 ULTRA-WS: Slow batch update: ${processingTime.toFixed(1)}ms (target: <16ms)`)
          } else {
            console.log(`⚡ ULTRA-WS: Batch update processed in ${processingTime.toFixed(1)}ms`)
          }
        }

      } catch (error) {
        console.error('❌ ULTRA-WS: Error processing WebSocket message:', error)
      }
    }

    processWebSocketMessage()
  }, [wsLastMessage, opts.enableRealTime, opts.enableAlerts, queryClient, dataCollector, alertSystem])
  
  // Refs para controle
  const previousOpportunities = useRef<ArbitrageOpportunity[]>([])
  const lastAlertCheck = useRef<number>(0)
  const performanceMetrics = useRef({
    responseTime: 0,
    cacheHitRate: 0.8,
    errorRate: 0
  })

  // Query principal para coleta de dados
  const {
    data,
    isLoading,
    isError,
    error,
    isRefetching,
    refetch,
    dataUpdatedAt
  } = useQuery({
    queryKey: ['arbitrage-data'],
    queryFn: async (): Promise<{
      opportunities: ArbitrageOpportunity[]
      metrics: DashboardMetrics
    }> => {
      const startTime = Date.now()
      
      try {
        console.log('🔄 useArbitrageData: Coletando dados das APIs reais...')
        
        // Coletar oportunidades usando APIs reais
        const opportunities = await dataCollector.collectAllData()
        
        // Verificar se MDT está presente
        const mdtOpportunities = opportunities.filter(opp => opp.symbol === 'MDT/USDT');
        console.log(`🔍 useArbitrageData: MDT encontradas: ${mdtOpportunities.length}`);
        if (mdtOpportunities.length > 0) {
          console.log(`🎯 useArbitrageData: MDT detalhes:`, mdtOpportunities[0]);
        }
        
        // Calcular métricas do dashboard
        const metrics = dataCollector.calculateDashboardMetrics(opportunities)
        
        // Atualizar métricas de performance
        const responseTime = Date.now() - startTime
        performanceMetrics.current.responseTime = responseTime
        
        console.log(`✅ useArbitrageData: ${opportunities.length} oportunidades coletadas em ${responseTime}ms`)
        
        return { opportunities, metrics }
        
      } catch (error) {
        console.error('❌ useArbitrageData: Erro na coleta:', error)
        
        // Atualizar taxa de erro
        performanceMetrics.current.errorRate = Math.min(
          performanceMetrics.current.errorRate + 0.1, 
          1
        )
        
        throw error
      }
    },
    enabled: opts.enabled,
    // 🚀 CRÍTICO: Desabilitar polling completamente - apenas WebSocket
    refetchInterval: false,  // 🚀 CRÍTICO: Zero polling HTTP
    staleTime: wsConnectionState === 'connected'
      ? Infinity        // 🚀 OTIMIZADO: Dados sempre frescos via WebSocket
      : (opts.staleTime || 1000),           // Cache curto para fallback HTTP
    gcTime: opts.cacheTime || 5000, // Cache mais longo para fallback
    retry: wsConnectionState === 'connected' ? 0 : 1, // Sem retry quando WebSocket funciona
    retryDelay: (attemptIndex) => Math.min(1000 * attemptIndex, 5000), // Retry mais lento
    refetchOnWindowFocus: false, // Evitar refetch desnecessário
    refetchOnReconnect: wsConnectionState !== 'connected' // Só reconectar se WebSocket falhou
  })

  // Detectar novas oportunidades para alertas
  const checkForNewOpportunities = useCallback((
    newOpportunities: ArbitrageOpportunity[]
  ) => {
    if (!opts.enableAlerts) return
    
    const now = Date.now()
    
    // Throttle de alertas (máximo 1 verificação por 5 segundos)
    if (now - lastAlertCheck.current < 5000) return
    
    lastAlertCheck.current = now
    
    // Verificar oportunidades de alta rentabilidade
    const highProfitOpportunities = newOpportunities.filter(
      opp => opp.profitability === 'HIGH' && Math.abs(opp.spreadPercentage) > 1.0
    )
    
    // Verificar novas oportunidades (que não estavam na lista anterior)
    const previousIds = new Set(previousOpportunities.current.map(opp => opp.id))
    const newHighProfitOpportunities = highProfitOpportunities.filter(
      opp => !previousIds.has(opp.id)
    )
    
    // Disparar alertas para novas oportunidades de alta rentabilidade
    newHighProfitOpportunities.forEach(opportunity => {
      alertSystem.checkCrossExchangeSpreadAlert(opportunity)
    })
    
    if (newHighProfitOpportunities.length > 0) {
      console.log(`🔔 useArbitrageData: ${newHighProfitOpportunities.length} novos alertas disparados`)
    }
    
    // Atualizar referência
    previousOpportunities.current = newOpportunities
    
  }, [opts.enableAlerts, alertSystem])

  // Effect para verificar alertas quando dados mudam
  useEffect(() => {
    if (data?.opportunities && opts.enableAlerts) {
      checkForNewOpportunities(data.opportunities)
    }
  }, [data?.opportunities, checkForNewOpportunities, opts.enableAlerts])

  // Effect para limpeza de cache antigo
  useEffect(() => {
    const cleanup = () => {
      // Limpar queries antigas (mais de 10 minutos)
      queryClient.removeQueries({
        queryKey: ['arbitrage-data'],
        exact: false,
        predicate: (query) => {
          const dataUpdatedAt = query.state.dataUpdatedAt
          return !!(dataUpdatedAt && Date.now() - dataUpdatedAt > 600000) // 10 minutos
        }
      })
      
      // Limpar histórico de alertas
      alertSystem.cleanupAlertHistory()
    }

    const interval = setInterval(cleanup, 300000) // 5 minutos
    return () => clearInterval(interval)
  }, [queryClient, alertSystem])

  // Calcular idade dos dados
  const dataAge = dataUpdatedAt ? Date.now() - dataUpdatedAt : 0
  const lastUpdate = dataUpdatedAt ? new Date(dataUpdatedAt) : null

  // Determinar status de conexão
  const connectionStatus: 'connected' | 'connecting' | 'disconnected' = 
    isLoading ? 'connecting' :
    isError ? 'disconnected' :
    'connected'

  // Função de refresh manual
  const manualRefetch = useCallback(() => {
    console.log('🔄 useArbitrageData: Refresh manual solicitado')
    refetch()
  }, [refetch])

  return {
    opportunities: data?.opportunities || [],
    metrics: data?.metrics || null,
    isLoading,
    isError,
    error: error as Error | null,
    isRefetching,
    refetch: manualRefetch,
    lastUpdate,
    dataAge,
    connectionStatus,
    performanceMetrics: performanceMetrics.current,
    // 🚀 ULTRA-WEBSOCKET: WebSocket integration properties
    websocket: {
      connectionState: wsConnectionState,
      messagesReceived: wsStats.messagesReceived,
      averageLatency: wsStats.averageLatency,
      uptime: wsStats.uptime,
      realTimeUpdates
    }
  }
}

// Hook auxiliar para métricas de performance
export function useArbitragePerformance() {
  const queryClient = useQueryClient()
  
  const getQueryStats = useCallback(() => {
    const queries = queryClient.getQueryCache().getAll()
    const arbitrageQueries = queries.filter(q => 
      q.queryKey[0] === 'arbitrage-data'
    )
    
    const totalQueries = arbitrageQueries.length
    const successfulQueries = arbitrageQueries.filter(q => 
      q.state.status === 'success'
    ).length
    const errorQueries = arbitrageQueries.filter(q => 
      q.state.status === 'error'
    ).length
    
    return {
      totalQueries,
      successfulQueries,
      errorQueries,
      successRate: totalQueries > 0 ? successfulQueries / totalQueries : 0,
      errorRate: totalQueries > 0 ? errorQueries / totalQueries : 0
    }
  }, [queryClient])
  
  const clearCache = useCallback(() => {
    queryClient.removeQueries({ queryKey: ['arbitrage-data'] })
    console.log('🧹 useArbitrageData: Cache limpo')
  }, [queryClient])
  
  return {
    getQueryStats,
    clearCache
  }
}

// Hook para controle de tempo real
export function useRealTimeArbitrage(enabled: boolean = true) {
  const queryClient = useQueryClient()
  
  const enableRealTime = useCallback(() => {
    queryClient.setQueryDefaults(['arbitrage-data'], {
      refetchInterval: 5000, // 5 segundos para tempo real
      staleTime: 1000 // 1 segundo
    })
    console.log('⚡ useArbitrageData: Modo tempo real ativado')
  }, [queryClient])
  
  const disableRealTime = useCallback(() => {
    queryClient.setQueryDefaults(['arbitrage-data'], {
      refetchInterval: 30000, // 30 segundos normal
      staleTime: 5000 // 5 segundos
    })
    console.log('🔄 useArbitrageData: Modo normal ativado')
  }, [queryClient])
  
  useEffect(() => {
    if (enabled) {
      enableRealTime()
    } else {
      disableRealTime()
    }
    
    return () => {
      disableRealTime() // Cleanup
    }
  }, [enabled, enableRealTime, disableRealTime])
  
  return {
    enableRealTime,
    disableRealTime
  }
}

export default useArbitrageData