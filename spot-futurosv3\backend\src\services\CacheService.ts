import { CacheEntry } from '../types/index.js';

/**
 * 🚀 ULTRA-FAST CacheService - Simplified for <10ms Access
 * 
 * Simplified 2-layer cache system optimized for ultra-low latency
 * Target: <1ms hot cache, <10ms warm cache
 */

interface SimpleCacheMetrics {
  hotHits: number;
  warmHits: number;
  totalMisses: number;
  hitRate: number;
}

export class CacheService {
  private static instance: CacheService;
  
  // 🚀 ULTRA-SIMPLE: 2-Layer Cache Architecture
  private hotCache = new Map<string, any>();                    // <1ms access, 500ms TTL
  private warmCache = new Map<string, CacheEntry<any>>();       // <10ms access, 2s TTL
  
  // Simplified metrics
  private metrics: SimpleCacheMetrics = {
    hotHits: 0,
    warmHits: 0,
    totalMisses: 0,
    hitRate: 0
  };
  
  // Legacy support
  private cache = new Map<string, CacheEntry<any>>();
  private defaultTTL = parseInt(process.env.CACHE_TTL || '500'); // 500ms default
  private hitCount = 0;
  private missCount = 0;

  /**
   * 🚀 ULTRA-FAST: Simplified singleton for maximum speed
   */
  static getInstance(): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService();
      
      // 🚀 ULTRA-CLEANUP: Simple cleanup every 5 seconds
      setInterval(() => {
        CacheService.instance.ultraFastCleanup();
      }, 5000);
      
      console.log('🚀 ULTRA-FAST: 2-layer cache initialized for <10ms access');
    }
    return CacheService.instance;
  }

  /**
   * 🚀 ULTRA-FAST: 2-layer cache with <10ms access
   * Target: <1ms hot, <10ms warm
   */
  getUltraFast<T>(key: string): T | null {
    // Hot cache: <1ms access
    if (this.hotCache.has(key)) {
      this.metrics.hotHits++;
      this.hitCount++;
      return this.hotCache.get(key) as T;
    }
    
    // Warm cache: <10ms access
    const entry = this.warmCache.get(key);
    if (entry && !this.isExpired(entry)) {
      // Promote to hot cache
      this.hotCache.set(key, entry.data);
      this.metrics.warmHits++;
      this.hitCount++;
      return entry.data as T;
    }
    
    // Cache miss
    this.metrics.totalMisses++;
    this.missCount++;
    return null;
  }

  /**
   * 🚀 ULTRA-FAST: Simple 2-layer caching with optimized TTL
   */
  setUltraFast<T>(key: string, data: T, isHot: boolean = false): void {
    if (isHot) {
      // Hot cache: 2s TTL (increased for better hit rate)
      this.hotCache.set(key, data);
      // Auto-expire after 2s
      setTimeout(() => {
        this.hotCache.delete(key);
      }, 2000);
    } else {
      // Warm cache: 5s TTL (increased for better hit rate)
      const entry: CacheEntry<T> = {
        data,
        timestamp: Date.now(),
        ttl: 5000
      };
      this.warmCache.set(key, entry);
    }
  }

  /**
   * 🚀 ULTRA-FAST: Primary get method
   */
  get<T>(key: string): T | null {
    return this.getUltraFast<T>(key);
  }

  /**
   * 🚀 ULTRA-FAST: Primary set method
   */
  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {
    const isHot = ttl <= 1000; // <1s = hot cache
    this.setUltraFast(key, data, isHot);
    
    // Also set in legacy cache for compatibility
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl
    };
    this.cache.set(key, entry);
  }

  /**
   * 🚀 ULTRA-FAST: Remove from both cache levels
   */
  delete(key: string): boolean {
    let deleted = false;
    
    if (this.hotCache.delete(key)) deleted = true;
    if (this.warmCache.delete(key)) deleted = true;
    if (this.cache.delete(key)) deleted = true;
    
    return deleted;
  }

  /**
   * 🚀 ULTRA-FAST: Clear all cache levels
   */
  clear(): void {
    this.hotCache.clear();
    this.warmCache.clear();
    this.cache.clear();
    
    // Reset metrics
    this.metrics = {
      hotHits: 0,
      warmHits: 0,
      totalMisses: 0,
      hitRate: 0
    };
    
    console.log('🧹 ULTRA-FAST: All cache levels cleared');
  }

  /**
   * 🚀 ULTRA-FAST: Simple cleanup for 2-layer cache
   */
  private ultraFastCleanup(): void {
    const now = Date.now();
    let evicted = 0;
    
    // Clean warm cache
    for (const [key, entry] of this.warmCache.entries()) {
      if (this.isExpired(entry)) {
        this.warmCache.delete(key);
        evicted++;
      }
    }
    
    // Clean legacy cache
    this.cleanup();
    
    // Update hit rate
    const totalRequests = this.metrics.hotHits + this.metrics.warmHits + this.metrics.totalMisses;
    if (totalRequests > 0) {
      this.metrics.hitRate = ((this.metrics.hotHits + this.metrics.warmHits) / totalRequests) * 100;
    }
    
    if (evicted > 0) {
      console.log(`🧹 ULTRA-FAST: Cleaned ${evicted} expired entries`);
    }
  }

  /**
   * 🚀 ULTRA-FAST: Simple cleanup method
   */
  cleanup(): void {
    const now = Date.now();
    
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 🚀 ULTRA-FAST: Check if entry is expired
   */
  private isExpired(entry: CacheEntry<any>): boolean {
    return (Date.now() - entry.timestamp) > entry.ttl;
  }

  /**
   * 🚀 ULTRA-SMART: Dynamic TTL based on data volatility and type
   */
  getDynamicTTL(key: string, data?: any): number {
    // 🚀 ULTRA-AGGRESSIVE: Different TTLs based on data volatility
    if (key.includes('opportunities')) {
      // Opportunities change frequently - very short TTL
      return 200; // 200ms for maximum freshness
    }

    if (key.includes('exchange_data')) {
      // Exchange data is moderately volatile
      return 800; // 800ms for balance of speed/freshness
    }

    if (key.includes('spot_') || key.includes('futures_')) {
      // Individual market data - medium volatility
      return 1000; // 1s for individual markets
    }

    if (key.includes('stats') || key.includes('health')) {
      // System stats change slowly
      return 5000; // 5s for system data
    }

    // Default for unknown data types
    return this.defaultTTL;
  }

  /**
   * 🚀 ULTRA-SMART: Set with dynamic TTL based on volatility
   */
  setDynamic<T>(key: string, data: T): void {
    const ttl = this.getDynamicTTL(key, data);
    this.set(key, data, ttl);

    // 🚀 DEBUG: Log TTL decisions for optimization
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔄 Cache: ${key} → TTL: ${ttl}ms`);
    }
  }

  /**
   * 🚀 ULTRA-FAST: Get cache statistics
   */
  getStats(): { 
    size: number; 
    keys: string[]; 
    hitRate: number;
    hitCount: number;
    missCount: number;
    hotCacheSize: number;
    warmCacheSize: number;
  } {
    const total = this.hitCount + this.missCount;
    const hitRate = total > 0 ? (this.hitCount / total) * 100 : 0;
    
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      hitRate: Math.round(hitRate * 100) / 100,
      hitCount: this.hitCount,
      missCount: this.missCount,
      hotCacheSize: this.hotCache.size,
      warmCacheSize: this.warmCache.size
    };
  }

  /**
   * 🚀 ULTRA-FAST: Reset statistics
   */
  resetStats(): void {
    this.hitCount = 0;
    this.missCount = 0;
    this.metrics = {
      hotHits: 0,
      warmHits: 0,
      totalMisses: 0,
      hitRate: 0
    };
    
    console.log('📊 ULTRA-FAST: All statistics reset');
  }
}