// DashboardMain - Componente Central do Dashboard de Arbitragem

import { useState, useEffect } from 'react'
import { RefreshCw, AlertCircle, TrendingUp, Activity, Wifi, WifiOff } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs'
import { StatsCards } from './StatsCards'
import { OpportunityTable } from '../opportunities/OpportunityTable'
import { useArbitrageData, useRealTimeArbitrage } from '@/hooks/useArbitrageData'
import { PositionManager } from '../positions/PositionManager'
import { RealTimeUpdates } from '../realtime/RealTimeUpdates'
import { NotificationSystem } from '../notifications/NotificationSystem'
import { WebSocketStatus } from '../websocket/WebSocketStatus'
import AuthStatus from '../auth/AuthStatus'
import DataQualityDashboard from '../data/DataQualityDashboard'
import APIMonitoringDashboard from '../monitoring/APIMonitoringDashboard'
import OptimizationDashboard from '../optimization/OptimizationDashboard'
import AdvancedAnalytics from '../analytics/AdvancedAnalytics'
import ReportGenerator from '../reports/ReportGenerator'
import SystemSettings from '../settings/SystemSettings'
import PerformanceDashboard from '../performance/PerformanceDashboard'
import PerformanceAlerts from '../performance/PerformanceAlerts'


interface DashboardMainProps {
  className?: string
}

export function DashboardMain({ className = '' }: DashboardMainProps) {
  const [realTimeEnabled, setRealTimeEnabled] = useState(true)

  // Hook principal para dados de arbitragem com APIs reais
  const {
    opportunities,
    metrics,
    isLoading,
    isError,
    error,
    isRefetching,
    refetch,
    lastUpdate,
    dataAge,
    connectionStatus,
    performanceMetrics,
    websocket
  } = useArbitrageData({
    enabled: true,
    refetchInterval: realTimeEnabled ? 1000 : 5000, // 🚀 ULTRA-FAST: 1s tempo real, 5s normal
    enableAlerts: true,
    enableRealTime: realTimeEnabled
  })

  // Hook para controle de tempo real
  useRealTimeArbitrage(realTimeEnabled)

  /**
   * Toggle tempo real
   */
  const toggleRealTime = () => {
    setRealTimeEnabled(!realTimeEnabled)
  }

  /**
   * Refresh manual
   */
  const handleManualRefresh = () => {
    refetch()
  }

  /**
   * Renderiza indicadores de loading
   */
  const renderLoadingState = () => (
    <div className="space-y-6">
      {/* Loading Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[1, 2, 3, 4].map(i => (
          <Card key={i} className="p-6">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            </div>
          </Card>
        ))}
      </div>

      {/* Loading Content */}
      <Card className="p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </Card>
    </div>
  )

  /**
   * Renderiza estado de erro
   */
  const renderErrorState = () => (
    <Card className="p-8 text-center">
      <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-red-700 mb-2">
        Erro ao Carregar Dados das APIs
      </h3>
      <p className="text-gray-600 mb-4">
        {error?.message || 'Erro na conexão com as exchanges'}
      </p>
      <div className="space-y-2 mb-4">
        <p className="text-sm text-gray-500">
          Status: {connectionStatus}
        </p>
        <p className="text-sm text-gray-500">
          Taxa de erro: {(performanceMetrics.errorRate * 100).toFixed(1)}%
        </p>
      </div>
      <Button onClick={handleManualRefresh} variant="outline" disabled={isRefetching}>
        <RefreshCw className={`h-4 w-4 mr-2 ${isRefetching ? 'animate-spin' : ''}`} />
        {isRefetching ? 'Tentando...' : 'Tentar Novamente'}
      </Button>
    </Card>
  )



  /**
   * Renderiza header com controles
   */
  const renderHeader = () => (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Dashboard de Arbitragem Cross-Exchange
        </h1>
        <p className="text-gray-600">
          Monitoramento em tempo real de oportunidades entre exchanges
        </p>
      </div>
      
      <div className="flex items-center gap-3">
        {/* Status da conexão */}
        <div className="flex items-center gap-2 text-sm">
          {connectionStatus === 'connected' ? (
            <Wifi className="h-4 w-4 text-green-500" />
          ) : connectionStatus === 'connecting' ? (
            <Wifi className="h-4 w-4 text-yellow-500 animate-pulse" />
          ) : (
            <WifiOff className="h-4 w-4 text-red-500" />
          )}
          <span className="text-gray-500">
            {connectionStatus === 'connected' ? 'APIs Conectadas' :
             connectionStatus === 'connecting' ? 'Conectando...' :
             'Desconectado'}
          </span>
        </div>
        
        {/* Status da última atualização */}
        <div className="text-sm text-gray-500">
          {lastUpdate ? (
            <>
              Última atualização: {lastUpdate.toLocaleTimeString()}
              {dataAge > 60000 && (
                <span className="text-yellow-600 ml-1">
                  ({Math.floor(dataAge / 1000)}s atrás)
                </span>
              )}
            </>
          ) : (
            'Carregando...'
          )}
        </div>
        
        {/* Performance metrics */}
        <div className="text-xs text-gray-400">
          {performanceMetrics.responseTime}ms
        </div>
        
        {/* Toggle tempo real */}
        <Button
          variant={realTimeEnabled ? "default" : "outline"}
          size="sm"
          onClick={toggleRealTime}
        >
          <Activity className={`h-4 w-4 mr-2 ${realTimeEnabled ? 'animate-pulse' : ''}`} />
          Tempo Real {realTimeEnabled ? 'ON' : 'OFF'}
        </Button>
        
        {/* 🚀 OTIMIZADO: Refresh manual apenas quando WebSocket falha */}
        {(!websocket?.isConnected && dataAge > 30000) && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleManualRefresh}
            disabled={isLoading || isRefetching}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${(isLoading || isRefetching) ? 'animate-spin' : ''}`} />
            Reconectar
          </Button>
        )}
      </div>
    </div>
  )

  /**
   * Renderiza conteúdo principal com tabs
   */
  const renderMainContent = () => (
    <Tabs defaultValue="opportunities" className="space-y-6">
      <TabsList className="grid w-full grid-cols-10">
        <TabsTrigger value="opportunities">
          Oportunidades
          {opportunities.length > 0 && (
            <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
              {opportunities.length}
            </span>
          )}
        </TabsTrigger>
        <TabsTrigger value="charts">Gráficos</TabsTrigger>
        <TabsTrigger value="exchanges">Exchanges</TabsTrigger>
        <TabsTrigger value="analytics">Analytics</TabsTrigger>
        <TabsTrigger value="monitoring">Monitoramento</TabsTrigger>
        <TabsTrigger value="performance">Performance</TabsTrigger>
        <TabsTrigger value="positions">Posições</TabsTrigger>
        <TabsTrigger value="optimization">Otimização</TabsTrigger>
        <TabsTrigger value="reports">Relatórios</TabsTrigger>
        <TabsTrigger value="settings">Configurações</TabsTrigger>
      </TabsList>

      <TabsContent value="opportunities" className="space-y-4">
        <OpportunityTable 
          opportunities={opportunities} 
          isLoading={isLoading}
        />
      </TabsContent>

      <TabsContent value="charts">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Gráficos e Métricas</h3>
          <p className="text-gray-500">
            Sistema de gráficos será implementado na próxima fase
          </p>
        </Card>
      </TabsContent>

      <TabsContent value="exchanges">
        <AuthStatus showDetails={true} />
      </TabsContent>

      <TabsContent value="analytics">
        <AdvancedAnalytics opportunities={opportunities} />
      </TabsContent>

      <TabsContent value="monitoring">
        <div className="space-y-6">
          <APIMonitoringDashboard />
          <RealTimeUpdates
            onDataUpdate={(data) => console.log('Real-time data update:', data.length)}
            showConnectionStatus={true}
            showUpdateIndicators={true}
            showLatencyInfo={true}
            websocketData={websocket}
          />
        </div>
      </TabsContent>

      <TabsContent value="performance">
        <PerformanceDashboard />
      </TabsContent>

      <TabsContent value="positions">
        <PositionManager />
      </TabsContent>

      <TabsContent value="optimization">
        <OptimizationDashboard />
      </TabsContent>

      <TabsContent value="reports">
        <ReportGenerator opportunities={opportunities} metrics={metrics} />
      </TabsContent>

      <TabsContent value="settings">
        <SystemSettings />
      </TabsContent>
    </Tabs>
  )

  return (
    <div className={`space-y-6 ${className}`}>
      {renderHeader()}
      
      {isLoading && opportunities.length === 0 ? (
        renderLoadingState()
      ) : isError ? (
        renderErrorState()
      ) : (
        <>
          <StatsCards metrics={metrics} isLoading={isLoading || isRefetching} />
          
          {/* 🚀 ULTRA-WEBSOCKET: WebSocket status display */}
          <div className="mb-6">
            <WebSocketStatus websocket={websocket} />
          </div>

          {/* 🚀 ADVANCED PERFORMANCE MONITORING: Performance alerts */}
          <div className="mb-6">
            <PerformanceAlerts maxAlerts={5} autoRefresh={true} />
          </div>
          
          {renderMainContent()}
        </>
      )}
    </div>
  )
}

export default DashboardMain