@echo off
echo 🚀 INICIANDO SISTEMA SPOT-FUTUROS V3
echo =====================================

echo.
echo 📋 Verificando dependências...

:: Verificar se Node.js está instalado
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js não encontrado! Instale o Node.js primeiro.
    pause
    exit /b 1
)

:: Verificar se npm está instalado
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm não encontrado! Instale o npm primeiro.
    pause
    exit /b 1
)

echo ✅ Node.js e npm encontrados!

echo.
echo 🔧 Instalando dependências do backend...
cd backend
if not exist node_modules (
    echo 📦 Instalando dependências do backend...
    npm install
    if errorlevel 1 (
        echo ❌ Erro ao instalar dependências do backend!
        pause
        exit /b 1
    )
) else (
    echo ✅ Dependências do backend já instaladas
)

echo.
echo 🖥️ Instalando dependências do frontend...
cd ..
if not exist node_modules (
    echo 📦 Instalando dependências do frontend...
    npm install
    if errorlevel 1 (
        echo ❌ Erro ao instalar dependências do frontend!
        pause
        exit /b 1
    )
) else (
    echo ✅ Dependências do frontend já instaladas
)

echo.
echo 🚀 Iniciando serviços...

:: Criar arquivo de log
echo %date% %time% - Sistema iniciado > system.log

:: Iniciar backend em nova janela
echo 🔧 Iniciando Backend (porta 3001)...
start "Backend - Spot Futuros V3" cmd /k "cd backend && echo 🔧 BACKEND INICIANDO... && npx tsx watch src/server.ts"

:: Aguardar 3 segundos para o backend iniciar
timeout /t 3 /nobreak >nul

:: Iniciar frontend em nova janela
echo 🖥️ Iniciando Frontend (porta 5002)...
start "Frontend - Spot Futuros V3" cmd /k "echo 🖥️ FRONTEND INICIANDO... && npx vite --port 5002"

:: Aguardar 2 segundos
timeout /t 2 /nobreak >nul

:: Abrir página de teste
echo 🔍 Abrindo página de teste...
start "" "test-complete-system.html"

:: Aguardar 3 segundos
timeout /t 3 /nobreak >nul

:: Abrir aplicação principal
echo 🌐 Abrindo aplicação principal...
start "" "http://localhost:5002"

echo.
echo 🎉 SISTEMA INICIADO COM SUCESSO!
echo =====================================
echo.
echo 📊 Serviços rodando:
echo   🔧 Backend:  http://localhost:3001
echo   🖥️ Frontend: http://localhost:5002
echo   🔍 Teste:    test-complete-system.html
echo.
echo 📝 Logs disponíveis em system.log
echo.
echo ⚠️  Para parar o sistema, feche as janelas do backend e frontend
echo.
pause
