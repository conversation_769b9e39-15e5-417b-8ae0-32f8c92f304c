import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { WebSocketServer } from 'ws';
import { createServer } from 'http';
import { ExchangeService } from './services/ExchangeService.js';
import { OpportunityBroadcaster } from './services/OpportunityBroadcaster.js';
import type { ArbitrageOpportunity } from './types/index.js';

// Carregar variáveis de ambiente
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: [
    process.env.CORS_ORIGIN || 'http://localhost:5002',
    'http://localhost:5003',
    'http://localhost:5004'
  ],
  credentials: true
}));

app.use(express.json());

// Rate limiting simples
const requestCounts = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_REQUESTS = parseInt(process.env.RATE_LIMIT_REQUESTS || '100');
const RATE_LIMIT_WINDOW = parseInt(process.env.RATE_LIMIT_WINDOW || '60000');

// Middleware de métricas
const metricsMiddleware = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const startTime = Date.now();
  
  const originalSend = res.send;
  res.send = function(data) {
    const responseTime = Date.now() - startTime;
    const isError = res.statusCode >= 400;
    
    import('./services/MetricsService.js').then(({ MetricsService }) => {
      const metricsService = MetricsService.getInstance();
      metricsService.recordApiCall(responseTime, isError);
    }).catch(console.error);
    
    return originalSend.call(this, data);
  };
  
  next();
};

const rateLimiter = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
  const now = Date.now();
  
  const clientData = requestCounts.get(clientIP);
  
  if (!clientData || now > clientData.resetTime) {
    requestCounts.set(clientIP, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    next();
  } else if (clientData.count < RATE_LIMIT_REQUESTS) {
    clientData.count++;
    next();
  } else {
    res.status(429).json({ 
      error: 'Rate limit exceeded',
      message: `Máximo ${RATE_LIMIT_REQUESTS} requests por minuto`
    });
  }
};

app.use(rateLimiter);
app.use(metricsMiddleware);

// Instância do serviço de exchanges
const exchangeService = new ExchangeService();

// Middleware de logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    env: {
      enableRealAPIs: process.env.ENABLE_REAL_APIS === 'true',
      nodeEnv: process.env.NODE_ENV || 'development'
    }
  });
});

// Exchange data endpoint
app.get('/api/exchanges/data', async (req, res) => {
  try {
    console.log('🚀 Coletando dados das exchanges...');
    const startTime = performance.now();
    
    const data = await exchangeService.getAllExchangeDataParallel();
    const processingTime = performance.now() - startTime;
    
    if (processingTime > 20) {
      console.warn(`🚨 Coleta lenta: ${processingTime.toFixed(1)}ms (target: <20ms)`);
    } else {
      console.log(`⚡ Coleta rápida: ${processingTime.toFixed(1)}ms ✅`);
    }
    
    res.json({
      success: true,
      data,
      processingTime: Math.round(processingTime),
      optimizationLevel: 'ULTRA',
      timestamp: new Date().toISOString(),
      metadata: data.metadata
    });
    
  } catch (error) {
    console.error('❌ Erro na coleta de dados:', error);
    res.status(500).json({
      success: false,
      error: 'Falha na coleta de dados',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// Arbitrage opportunities endpoint
app.get('/api/arbitrage/opportunities', async (req, res) => {
  try {
    console.log('🎯 Calculando oportunidades de arbitragem...');
    const startTime = performance.now();
    
    const opportunities = await exchangeService.calculateArbitrageOpportunitiesUltra();
    const processingTime = performance.now() - startTime;
    
    if (processingTime > 100) {
      console.warn(`🚨 Cálculo lento: ${processingTime.toFixed(1)}ms (target: <100ms)`);
    } else {
      console.log(`⚡ Cálculo rápido: ${processingTime.toFixed(1)}ms ✅`);
    }

    // Broadcast opportunities via WebSocket
    if (opportunities.length > 0) {
      opportunities.forEach(opportunity => {
        broadcaster.broadcast(opportunity);
      });
    }

    res.json({
      success: true,
      opportunities,
      count: opportunities.length,
      processingTime: Math.round(processingTime),
      optimizationLevel: 'ULTRA',
      timestamp: new Date().toISOString(),
      websocket: {
        connected: broadcaster.getStats().connectedClients,
        broadcasted: opportunities.length
      }
    });
    
  } catch (error) {
    console.error('❌ Erro no cálculo de oportunidades:', error);
    res.status(500).json({
      success: false,
      error: 'Falha no cálculo de oportunidades',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// Cache stats endpoint
app.get('/api/cache/stats', async (req, res) => {
  try {
    const { CacheService } = await import('./services/CacheService.js');
    const cacheService = CacheService.getInstance();
    const stats = cacheService.getStats();
    
    res.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao obter estatísticas do cache:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao obter estatísticas do cache',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// WebSocket stats endpoint
app.get('/api/websocket/stats', async (req, res) => {
  try {
    const wsStats = broadcaster.getStats();
    
    res.json({
      success: true,
      data: {
        ...wsStats,
        uptime: process.uptime(),
        serverTime: new Date().toISOString(),
        performance: {
          averageLatency: wsStats.averageLatency,
          messagesPerSecond: wsStats.messagesSent / (process.uptime() || 1),
          clientsConnected: wsStats.connectedClients,
          queueHealth: wsStats.queueLength < 100 ? 'healthy' : 'congested'
        }
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao obter estatísticas do WebSocket:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao obter estatísticas do WebSocket',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// Metrics health endpoint
app.get('/api/metrics/health', async (req, res) => {
  try {
    const { MetricsService } = await import('./services/MetricsService.js');
    const metricsService = MetricsService.getInstance();
    const healthMetrics = metricsService.getSystemHealth();
    
    res.json({
      success: true,
      data: healthMetrics,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao obter métricas de saúde:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao obter métricas de saúde',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// Advanced latency metrics endpoint
app.get('/api/metrics/advanced-latency', async (req, res) => {
  try {
    const { MetricsService } = await import('./services/MetricsService.js');
    const metricsService = MetricsService.getInstance();
    const advancedMetrics = metricsService.getAdvancedLatencyMetrics();
    
    res.json({
      success: true,
      data: advancedMetrics,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao obter métricas avançadas:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao obter métricas avançadas',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// Performance summary endpoint
app.get('/api/metrics/performance-summary', async (req, res) => {
  try {
    const { MetricsService } = await import('./services/MetricsService.js');
    const metricsService = MetricsService.getInstance();
    const summary = metricsService.getPerformanceSummary();
    
    res.json({
      success: true,
      data: summary,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao obter resumo de performance:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao obter resumo de performance',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// Sliding windows endpoint
app.get('/api/metrics/sliding-windows', async (req, res) => {
  try {
    const { MetricsService } = await import('./services/MetricsService.js');
    const metricsService = MetricsService.getInstance();
    const windows = metricsService.getSlidingWindows();
    
    res.json({
      success: true,
      data: windows,
      count: windows.length,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao obter janelas deslizantes:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao obter janelas deslizantes',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// WebSocket performance metrics endpoint
app.get('/api/metrics/websocket-performance', async (req, res) => {
  try {
    const wsStats = broadcaster.getStats();
    
    const performanceMetrics = {
      latencyMetrics: {
        average: wsStats.averageLatency,
        current: wsStats.averageLatency,
        target: 10 // ms
      },
      throughputMetrics: {
        messagesPerSecond: wsStats.messagesSent / (process.uptime() || 1),
        totalMessages: wsStats.messagesSent,
        queueLength: wsStats.queueLength
      },
      connectionMetrics: {
        activeConnections: wsStats.connectedClients,
        totalConnections: wsStats.totalConnections,
        uptime: process.uptime()
      },
      healthStatus: {
        overall: wsStats.queueLength < 100 && wsStats.averageLatency < 50 ? 'healthy' : 'degraded',
        queueHealth: wsStats.queueLength < 100 ? 'healthy' : 'congested',
        latencyHealth: wsStats.averageLatency < 50 ? 'healthy' : 'slow'
      }
    };
    
    res.json({
      success: true,
      data: performanceMetrics,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao obter métricas de performance WebSocket:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao obter métricas de performance WebSocket',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// Feature flags endpoints
app.get('/api/feature-flags', async (req, res) => {
  try {
    const { FeatureFlagService } = await import('./services/FeatureFlagService.js');
    const flagService = FeatureFlagService.getInstance();
    const flags = flagService.getAllFlags();
    const statistics = flagService.getStatistics();
    
    res.json({
      success: true,
      data: {
        flags,
        statistics
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao obter feature flags:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao obter feature flags',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

app.get('/api/feature-flags/:flagName', async (req, res) => {
  try {
    const { FeatureFlagService } = await import('./services/FeatureFlagService.js');
    const flagService = FeatureFlagService.getInstance();
    const { flagName } = req.params;
    
    const evaluation = flagService.evaluateFlag(flagName);
    
    res.json({
      success: true,
      data: {
        flagName,
        enabled: evaluation.enabled,
        rolloutPercentage: evaluation.rolloutPercentage,
        reason: evaluation.reason,
        metadata: evaluation.metadata
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao avaliar feature flag:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao avaliar feature flag',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// Rollback endpoints
app.get('/api/rollback/triggers', async (req, res) => {
  try {
    const { RollbackService } = await import('./services/RollbackService.js');
    const rollbackService = RollbackService.getInstance();
    const triggers = rollbackService.getTriggers();
    const statistics = rollbackService.getStatistics();
    
    res.json({
      success: true,
      data: {
        triggers,
        statistics
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao obter triggers de rollback:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao obter triggers de rollback',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

app.get('/api/rollback/history', async (req, res) => {
  try {
    const { RollbackService } = await import('./services/RollbackService.js');
    const rollbackService = RollbackService.getInstance();
    const history = rollbackService.getHistory();
    
    res.json({
      success: true,
      data: history,
      count: history.length,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao obter histórico de rollback:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao obter histórico de rollback',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

app.post('/api/rollback/manual', async (req, res) => {
  try {
    const { actions, reason } = req.body;
    
    if (!actions || !Array.isArray(actions) || actions.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Ações de rollback são obrigatórias',
        message: 'Forneça um array de ações para executar o rollback',
        timestamp: new Date().toISOString()
      });
    }
    
    const { RollbackService } = await import('./services/RollbackService.js');
    const rollbackService = RollbackService.getInstance();
    
    const result = await rollbackService.executeManualRollback(actions, reason || 'Manual rollback');
    
    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao executar rollback manual:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao executar rollback manual',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// Dashboard metrics endpoint
app.get('/api/metrics/dashboard', async (req, res) => {
  try {
    const { MetricsService } = await import('./services/MetricsService.js');
    const metricsService = MetricsService.getInstance();
    
    const dashboardData = {
      realTime: {
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage()
      },
      performance: metricsService.getPerformanceSummary(),
      health: metricsService.getSystemHealth(),
      websocket: broadcaster.getStats()
    };
    
    res.json({
      success: true,
      data: dashboardData,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao obter dados do dashboard:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao obter dados do dashboard',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// 🚀 ULTRA-WEBSOCKET: Initialize broadcaster
const broadcaster = new OpportunityBroadcaster();

// 🚀 ULTRA-WEBSOCKET: WebSocket connection handling
const server = createServer(app);
broadcaster.initialize(server);

// 🚀 CRÍTICO: Cache para evitar recálculo constante
let cachedOpportunities: any[] = [];
let lastCalculation = 0;
const CACHE_DURATION = 2000; // 2 segundos de cache

// 🚀 ULTRA-REAL-TIME: Auto-broadcast opportunities every 1 second
setInterval(async () => {
  try {
    if (broadcaster.getStats().connectedClients > 0) {
      console.log('🔄 Auto-broadcasting opportunities to connected clients...');
      const startTime = performance.now();

      // 🚀 CRÍTICO: Usar cache se disponível
      let opportunities;
      if (Date.now() - lastCalculation < CACHE_DURATION && cachedOpportunities.length > 0) {
        opportunities = cachedOpportunities;
        console.log('⚡ Usando cache - 0ms');
      } else {
        opportunities = await exchangeService.calculateArbitrageOpportunitiesUltra();
        cachedOpportunities = opportunities;
        lastCalculation = Date.now();
      }

      if (opportunities.length > 0) {
        // 🚀 ULTRA-OTIMIZADO: Filtrar apenas oportunidades de alta qualidade
        const relevantOpportunities = opportunities.filter(opp => {
          const absSpread = Math.abs(opp.spreadPercentage);
          return absSpread > 0.5 && // Apenas spreads > 0.5%
                 absSpread < 50 &&   // Filtrar spreads irreais
                 opp.spotVolume > 1000; // Volume mínimo
        });

        if (relevantOpportunities.length > 0) {
          // Broadcast all relevant opportunities at once for better performance
          broadcaster.broadcastMultiple(relevantOpportunities);

          const processingTime = performance.now() - startTime;
          console.log(`📡 Auto-broadcast: ${relevantOpportunities.length}/${opportunities.length} opportunities sent to ${broadcaster.getStats().connectedClients} clients in ${processingTime.toFixed(1)}ms`);
        }
      }
    }
  } catch (error) {
    console.error('❌ Error in auto-broadcast:', error);
  }
}, 1000); // 🚀 OTIMIZADO: Every 1 second for near real-time updates

// Iniciar servidor
server.listen(PORT, () => {
  console.log(`🚀 Servidor backend iniciado na porta ${PORT}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`📡 WebSocket disponível para conexões`);
  console.log(`🎯 Ambiente: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔑 APIs reais: ${process.env.ENABLE_REAL_APIS === 'true' ? 'ATIVADAS' : 'DESATIVADAS'}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Recebido SIGTERM, encerrando servidor...');
  broadcaster.cleanup();
  server.close(() => {
    console.log('✅ Servidor encerrado graciosamente');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 Recebido SIGINT, encerrando servidor...');
  broadcaster.cleanup();
  server.close(() => {
    console.log('✅ Servidor encerrado graciosamente');
    process.exit(0);
  });
});

// 🚀 ULTRA-WEBSOCKET: Export broadcaster for use in other modules
export { broadcaster };

export default app;