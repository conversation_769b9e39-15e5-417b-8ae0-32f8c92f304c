#!/usr/bin/env node

/**
 * 🔍 DEBUG: Por que estamos retornando 0 oportunidades?
 */

import fetch from 'node-fetch';

const BACKEND_URL = 'http://localhost:3001';

console.log('🔍 DEBUGANDO PROBLEMA DE 0 OPORTUNIDADES');
console.log('=' .repeat(50));

async function debugOpportunities() {
  // 1. Verificar dados das exchanges
  console.log('1. Verificando dados das exchanges...\n');
  
  try {
    const response = await fetch(`${BACKEND_URL}/api/exchanges/data`);
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Exchange Data: ${data.processingTime}ms`);
      console.log(`📊 Total Pairs: ${data.metadata?.totalPairs || 0}`);
      console.log(`📊 Exchanges: ${data.metadata?.exchanges?.join(', ') || 'N/A'}`);
      console.log(`📊 Errors: ${data.metadata?.errors || 0}`);
      
      if (data.data) {
        console.log(`📊 Spot Data: ${data.data.allSpotData?.length || 0} exchanges`);
        console.log(`📊 Futures Data: ${data.data.allFuturesData?.length || 0} exchanges`);
        
        // Verificar se temos dados
        if (data.data.allSpotData && data.data.allSpotData.length > 0) {
          const firstSpot = data.data.allSpotData[0];
          console.log(`📊 Primeira exchange spot: ${firstSpot.exchange} (${firstSpot.spot?.length || 0} pares)`);
          
          if (firstSpot.spot && firstSpot.spot.length > 0) {
            console.log(`📊 Exemplo spot: ${firstSpot.spot[0].symbol} - $${firstSpot.spot[0].price}`);
          }
        }
        
        if (data.data.allFuturesData && data.data.allFuturesData.length > 0) {
          const firstFutures = data.data.allFuturesData[0];
          console.log(`📊 Primeira exchange futures: ${firstFutures.exchange} (${firstFutures.futures?.length || 0} pares)`);
          
          if (firstFutures.futures && firstFutures.futures.length > 0) {
            console.log(`📊 Exemplo futures: ${firstFutures.futures[0].symbol} - $${firstFutures.futures[0].price}`);
          }
        }
      }
    } else {
      console.log(`❌ Erro HTTP: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Erro: ${error.message}`);
  }
  
  console.log('\n');
  
  // 2. Verificar cálculo de oportunidades
  console.log('2. Verificando cálculo de oportunidades...\n');
  
  try {
    const response = await fetch(`${BACKEND_URL}/api/arbitrage/opportunities`);
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Opportunities: ${data.processingTime}ms`);
      console.log(`📊 Count: ${data.count || 0}`);
      console.log(`📊 Success: ${data.success}`);
      
      if (data.opportunities && data.opportunities.length > 0) {
        console.log(`📊 Primeira oportunidade:`);
        const first = data.opportunities[0];
        console.log(`   Symbol: ${first.symbol}`);
        console.log(`   Spread: ${first.spreadPercentage?.toFixed(2)}%`);
        console.log(`   Spot: ${first.spotExchange} - $${first.spotPrice}`);
        console.log(`   Futures: ${first.futuresExchange} - $${first.futuresPrice}`);
      } else {
        console.log(`⚠️ Nenhuma oportunidade encontrada`);
      }
      
      // Verificar WebSocket broadcast
      if (data.websocket) {
        console.log(`📡 WebSocket: ${data.websocket.connected} conectados, ${data.websocket.broadcasted} enviadas`);
      }
    } else {
      console.log(`❌ Erro HTTP: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Erro: ${error.message}`);
  }
  
  console.log('\n');
  
  // 3. Verificar cache
  console.log('3. Verificando cache...\n');
  
  try {
    const response = await fetch(`${BACKEND_URL}/api/cache/stats`);
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Cache Stats:`);
      console.log(`📊 Hit Rate: ${data.data?.hitRate || 0}%`);
      console.log(`📊 Total Entries: ${data.data?.totalEntries || 0}`);
      console.log(`📊 Hot Hits: ${data.data?.hotHits || 0}`);
      console.log(`📊 Warm Hits: ${data.data?.warmHits || 0}`);
      console.log(`📊 Misses: ${data.data?.totalMisses || 0}`);
    }
  } catch (error) {
    console.log(`❌ Erro: ${error.message}`);
  }
}

debugOpportunities().catch(console.error);