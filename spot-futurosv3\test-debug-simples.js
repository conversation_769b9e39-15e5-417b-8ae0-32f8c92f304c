#!/usr/bin/env node

/**
 * 🔍 TESTE SIMPLES PARA VER LOGS DO BACKEND
 */

import fetch from 'node-fetch';

const BACKEND_URL = 'http://localhost:3001';

console.log('🔍 FAZENDO CHAMADA SIMPLES PARA VER LOGS...');

async function testSimple() {
  try {
    const response = await fetch(`${BACKEND_URL}/api/arbitrage/opportunities`);
    const data = await response.json();
    
    console.log(`📊 Resultado: ${data.count} oportunidades em ${data.processingTime}ms`);
    
    if (data.opportunities && data.opportunities.length > 0) {
      console.log('✅ Primeira oportunidade:', data.opportunities[0]);
    } else {
      console.log('⚠️ Nenhuma oportunidade - verifique logs do backend');
    }
    
  } catch (error) {
    console.log(`❌ Erro: ${error.message}`);
  }
}

testSimple();