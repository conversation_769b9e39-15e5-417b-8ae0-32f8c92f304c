/**
 * 🚀 ULTRA-FAST ExchangeService - APIs REAIS com Latência <800ms
 * 
 * Sistema ultra-otimizado para coleta de dados reais das exchanges
 * Target: <200ms por exchange, <800ms total
 */

import axios, { AxiosInstance } from 'axios';
import https from 'https';
import http from 'http';
import crypto from 'crypto';
import { CacheService } from './CacheService.js';
import type { SpotData, FuturesData, ArbitrageOpportunity } from '../types/index.js';

interface ExchangeData {
  exchange: string;
  spot: SpotData[];
  futures: FuturesData[];
  timestamp: number;
  status: string;
}

interface AllExchangeData {
  allSpotData: ExchangeData[];
  allFuturesData: ExchangeData[];
  metadata: {
    timestamp: number;
    processingTime: number;
    totalPairs: number;
    exchanges: string[];
    errors: number;
    warnings: number;
  };
}

export class ExchangeService {
  private cache: CacheService;
  private gateioClient: AxiosInstance;
  private mexcClient: AxiosInstance;
  private bitgetClient: AxiosInstance;

  // 🚀 ULTRA-OPTIMIZATION: Shared timestamp to reduce Date.now() calls
  private sharedTimestamp: number = Date.now();
  private timestampUpdateInterval: NodeJS.Timeout;

  constructor() {
    this.cache = CacheService.getInstance();
    this.initializeClients();

    // 🚀 Update shared timestamp every 100ms for performance
    this.timestampUpdateInterval = setInterval(() => {
      this.sharedTimestamp = Date.now();
    }, 100);
  }

  /**
   * 🚀 ULTRA-PARALLEL: Process array in parallel batches
   */
  private async processInParallel<T, R>(
    items: T[],
    processor: (item: T) => R | null,
    batchSize: number = 100
  ): Promise<R[]> {
    const results: R[] = [];
    const batches: T[][] = [];

    // Split into batches
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }

    // Process batches in parallel
    const batchPromises = batches.map(async (batch) => {
      const batchResults: R[] = [];
      for (const item of batch) {
        try {
          const result = processor(item);
          if (result) batchResults.push(result);
        } catch (error) {
          console.warn('⚠️ Error processing item:', error);
        }
      }
      return batchResults;
    });

    const batchResults = await Promise.all(batchPromises);
    return batchResults.flat();
  }

  /**
   * 🚀 ULTRA-FAST: Initialize HTTP clients with aggressive timeouts and connection pooling
   */
  private initializeClients(): void {
    // 🚀 ULTRA-FAST HTTP Agent with aggressive connection pooling
    const httpsAgent = new https.Agent({
      keepAlive: true,
      maxSockets: 50,           // Max concurrent connections per host
      maxFreeSockets: 10,       // Keep 10 connections open
      timeout: 200              // 200ms connection timeout
    });

    const httpAgent = new http.Agent({
      keepAlive: true,
      maxSockets: 50,
      maxFreeSockets: 10,
      timeout: 200
    });

    const ultraConfig = {
      timeout: parseInt(process.env.API_TIMEOUT || '200'), // 200ms timeout for ultra-fast
      httpsAgent: httpsAgent,
      httpAgent: httpAgent,
      headers: {
        'User-Agent': 'Ultra-Arbitrage-Bot/1.0',
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Connection': 'keep-alive',
        'Keep-Alive': 'timeout=5, max=1000'
      }
    };

    this.gateioClient = axios.create({
      baseURL: process.env.GATEIO_API_URL || 'https://api.gateio.ws',
      ...ultraConfig
    });

    this.mexcClient = axios.create({
      baseURL: process.env.MEXC_API_URL || 'https://api.mexc.com',
      ...ultraConfig
    });

    this.bitgetClient = axios.create({
      baseURL: process.env.BITGET_API_URL || 'https://api.bitget.com',
      ...ultraConfig
    });

    console.log('🚀 ULTRA-FAST: HTTP clients initialized with 200ms timeout + connection pooling');
  }

  /**
   * 🚀 ULTRA-PARALLEL: Coleta REAL de dados com processamento paralelo
   * Target: <800ms total
   */
  async getAllExchangeDataParallel(): Promise<AllExchangeData> {
    const startTime = performance.now();
    console.log('🚀 ULTRA-FAST: Starting REAL API collection...');

    try {
      // 🔥 PARALLEL EXECUTION: All 3 exchanges simultaneously
      const [gateioResult, mexcResult, bitgetResult] = await Promise.allSettled([
        this.fetchGateioDataReal(),
        this.fetchMexcDataReal(),
        this.fetchBitgetDataReal()
      ]);

      const allSpotData: ExchangeData[] = [];
      const allFuturesData: ExchangeData[] = [];
      let errors = 0;
      let warnings = 0;
      let totalPairs = 0;

      // Process Gate.io results
      if (gateioResult.status === 'fulfilled') {
        allSpotData.push(gateioResult.value.spot);
        allFuturesData.push(gateioResult.value.futures);
        totalPairs += gateioResult.value.spot.spot.length + gateioResult.value.futures.futures.length;
      } else {
        console.error('❌ Gate.io failed:', gateioResult.reason);
        errors++;
      }

      // Process MEXC results
      if (mexcResult.status === 'fulfilled') {
        allSpotData.push(mexcResult.value.spot);
        allFuturesData.push(mexcResult.value.futures);
        totalPairs += mexcResult.value.spot.spot.length + mexcResult.value.futures.futures.length;
      } else {
        console.error('❌ MEXC failed:', mexcResult.reason);
        errors++;
      }

      // Process Bitget results
      if (bitgetResult.status === 'fulfilled') {
        allSpotData.push(bitgetResult.value.spot);
        allFuturesData.push(bitgetResult.value.futures);
        totalPairs += bitgetResult.value.spot.spot.length + bitgetResult.value.futures.futures.length;
      } else {
        console.error('❌ Bitget failed:', bitgetResult.reason);
        errors++;
      }

      const processingTime = performance.now() - startTime;

      // 🚨 ULTRA-AGGRESSIVE ALERT: Target <800ms
      if (processingTime > 800) {
        console.warn(`🚨 ULTRA-SLOW: ${processingTime.toFixed(1)}ms (target: <800ms)`);
        warnings++;
      } else {
        console.log(`⚡ ULTRA-FAST: ${processingTime.toFixed(1)}ms (target: <800ms) ✅`);
      }

      return {
        allSpotData,
        allFuturesData,
        metadata: {
          timestamp: Date.now(),
          processingTime,
          totalPairs,
          exchanges: ['gateio', 'mexc', 'bitget'],
          errors,
          warnings
        }
      };

    } catch (error) {
      console.error('❌ ULTRA-FAST: Critical error in parallel collection:', error);
      throw error;
    }
  }

  /**
   * 🎯 GATE.IO: Real API data collection with HMAC Authentication
   */
  private async fetchGateioDataReal(): Promise<{spot: ExchangeData, futures: ExchangeData}> {
    const startTime = performance.now();
    
    try {
      // Check cache first
      const cacheKey = 'gateio_data';
      const cached = this.cache.get(cacheKey);
      if (cached) {
        console.log('⚡ Gate.io: Cache hit');
        return cached;
      }

      console.log('🔍 Gate.io: Fetching with HMAC authentication...');

      // Generate HMAC signature for Gate.io
      const timestamp = Math.floor(Date.now() / 1000).toString();
      const method = 'GET';
      const spotPath = '/api/v4/spot/tickers';
      const futuresPath = '/api/v4/futures/usdt/tickers';
      
      const spotSignature = this.generateGateioSignature(method, spotPath, '', timestamp);
      const futuresSignature = this.generateGateioSignature(method, futuresPath, '', timestamp);

      // Parallel spot and futures requests with HMAC headers
      const [spotResponse, futuresResponse] = await Promise.allSettled([
        this.gateioClient.get(spotPath, {
          headers: {
            'KEY': process.env.GATEIO_API_KEY,
            'Timestamp': timestamp,
            'SIGN': spotSignature
          }
        }).catch(err => {
          console.error('❌ Gate.io spot API error:', err.message);
          return { data: [] };
        }),
        this.gateioClient.get(futuresPath, {
          headers: {
            'KEY': process.env.GATEIO_API_KEY,
            'Timestamp': timestamp,
            'SIGN': futuresSignature
          }
        }).catch(err => {
          console.error('❌ Gate.io futures API error:', err.message);
          return { data: [] };
        })
      ]);

      const spotData: SpotData[] = [];
      const futuresData: FuturesData[] = [];

      // Process spot data with better error handling
      if (spotResponse.status === 'fulfilled' && spotResponse.value.data) {
        const tickers = Array.isArray(spotResponse.value.data) ? spotResponse.value.data : [];
        console.log(`� Gate.io: Processing ${tickers.length} spot tickers in parallel batches`);

        const normalizedSpotData = await this.processInParallel(
          tickers,
          (ticker) => this.normalizeGateioSpotData(ticker),
          100 // Process 100 items per batch
        );

        spotData.push(...normalizedSpotData.filter(data => data && data.price > 0));
      } else {
        console.warn('⚠️ Gate.io: No spot data received');
      }

      // Process futures data with better error handling
      if (futuresResponse.status === 'fulfilled' && futuresResponse.value.data) {
        const tickers = Array.isArray(futuresResponse.value.data) ? futuresResponse.value.data : [];
        console.log(`� Gate.io: Processing ${tickers.length} futures tickers in parallel batches`);

        const normalizedFuturesData = await this.processInParallel(
          tickers,
          (ticker) => this.normalizeGateioFuturesData(ticker),
          100 // Process 100 items per batch
        );

        futuresData.push(...normalizedFuturesData.filter(data => data && data.price > 0));
      } else {
        console.warn('⚠️ Gate.io: No futures data received');
      }

      const result = {
        spot: {
          exchange: 'gateio',
          spot: spotData,
          futures: [],
          timestamp: Date.now(),
          status: 'success'
        },
        futures: {
          exchange: 'gateio',
          spot: [],
          futures: futuresData,
          timestamp: Date.now(),
          status: 'success'
        }
      };

      // Cache for 500ms
      this.cache.set(cacheKey, result, 500);

      const processingTime = performance.now() - startTime;
      console.log(`⚡ Gate.io: ${spotData.length + futuresData.length} pairs in ${processingTime.toFixed(1)}ms`);

      return result;

    } catch (error) {
      console.error('❌ Gate.io critical error:', error);
      
      // Return empty data instead of throwing
      return {
        spot: {
          exchange: 'gateio',
          spot: [],
          futures: [],
          timestamp: Date.now(),
          status: 'error'
        },
        futures: {
          exchange: 'gateio',
          spot: [],
          futures: [],
          timestamp: Date.now(),
          status: 'error'
        }
      };
    }
  }

  /**
   * 🎯 MEXC: Real API data collection
   */
  private async fetchMexcDataReal(): Promise<{spot: ExchangeData, futures: ExchangeData}> {
    const startTime = performance.now();
    
    try {
      // Check cache first
      const cacheKey = 'mexc_data';
      const cached = this.cache.get(cacheKey);
      if (cached) {
        console.log('⚡ MEXC: Cache hit');
        return cached;
      }

      // Parallel spot and futures requests
      const [spotResponse, futuresResponse] = await Promise.allSettled([
        this.mexcClient.get('/api/v3/ticker/24hr'),
        this.mexcClient.get('/api/v1/contract/ticker')
      ]);

      const spotData: SpotData[] = [];
      const futuresData: FuturesData[] = [];

      // Process spot data with detailed logging
      if (spotResponse.status === 'fulfilled' && spotResponse.value.data) {
        const tickers = Array.isArray(spotResponse.value.data) ? spotResponse.value.data : [];
        console.log(`� MEXC: Processing ${tickers.length} spot tickers in parallel batches`);

        const normalizedSpotData = await this.processInParallel(
          tickers,
          (ticker) => this.normalizeMexcSpotData(ticker),
          100 // Process 100 items per batch
        );

        spotData.push(...normalizedSpotData.filter(data => data && data.price > 0));
      } else {
        console.warn('⚠️ MEXC: No spot data received');
      }

      // Process futures data with detailed logging
      if (futuresResponse.status === 'fulfilled' && futuresResponse.value.data) {
        const tickers = Array.isArray(futuresResponse.value.data) ? futuresResponse.value.data : [];
        console.log(`� MEXC: Processing ${tickers.length} futures tickers in parallel batches`);

        const normalizedFuturesData = await this.processInParallel(
          tickers,
          (ticker) => this.normalizeMexcFuturesData(ticker),
          100 // Process 100 items per batch
        );

        futuresData.push(...normalizedFuturesData.filter(data => data && data.price > 0));
      } else {
        console.warn('⚠️ MEXC: No futures data received');
      }

      const result = {
        spot: {
          exchange: 'mexc',
          spot: spotData,
          futures: [],
          timestamp: Date.now(),
          status: 'success'
        },
        futures: {
          exchange: 'mexc',
          spot: [],
          futures: futuresData,
          timestamp: Date.now(),
          status: 'success'
        }
      };

      // Cache for 500ms
      this.cache.set(cacheKey, result, 500);

      const processingTime = performance.now() - startTime;
      console.log(`⚡ MEXC: ${spotData.length + futuresData.length} pairs in ${processingTime.toFixed(1)}ms`);

      return result;

    } catch (error) {
      console.error('❌ MEXC error:', error);
      throw error;
    }
  }

  /**
   * 🎯 BITGET: Real API data collection (FIXED)
   */
  private async fetchBitgetDataReal(): Promise<{spot: ExchangeData, futures: ExchangeData}> {
    const startTime = performance.now();
    
    try {
      // Check cache first
      const cacheKey = 'bitget_data';
      const cached = this.cache.get(cacheKey);
      if (cached) {
        console.log('⚡ Bitget: Cache hit');
        return cached;
      }

      console.log('🔍 Bitget: Fetching real data...');

      // Parallel spot and futures requests with better error handling
      const [spotResponse, futuresResponse] = await Promise.allSettled([
        this.bitgetClient.get('/api/spot/v1/market/tickers').catch(err => {
          console.error('❌ Bitget spot API error:', err.message);
          return { data: [] };
        }),
        this.bitgetClient.get('/api/mix/v1/market/tickers?productType=UMCBL').catch(err => {
          console.error('❌ Bitget futures API error:', err.message);
          return { data: [] };
        })
      ]);

      const spotData: SpotData[] = [];
      const futuresData: FuturesData[] = [];

      // Process spot data with better error handling
      if (spotResponse.status === 'fulfilled' && spotResponse.value.data) {
        const tickers = Array.isArray(spotResponse.value.data) ? spotResponse.value.data : [];
        console.log(`� Bitget: Processing ${tickers.length} spot tickers in parallel batches`);

        const normalizedSpotData = await this.processInParallel(
          tickers,
          (ticker) => this.normalizeBitgetSpotData(ticker),
          100 // Process 100 items per batch
        );

        spotData.push(...normalizedSpotData.filter(data => data && data.price > 0));
      } else {
        console.warn('⚠️ Bitget: No spot data received');
      }

      // Process futures data with better error handling
      if (futuresResponse.status === 'fulfilled' && futuresResponse.value.data) {
        const tickers = Array.isArray(futuresResponse.value.data) ? futuresResponse.value.data : [];
        console.log(`� Bitget: Processing ${tickers.length} futures tickers in parallel batches`);

        const normalizedFuturesData = await this.processInParallel(
          tickers,
          (ticker) => this.normalizeBitgetFuturesData(ticker),
          100 // Process 100 items per batch
        );

        futuresData.push(...normalizedFuturesData.filter(data => data && data.price > 0));
      } else {
        console.warn('⚠️ Bitget: No futures data received');
      }

      const result = {
        spot: {
          exchange: 'bitget',
          spot: spotData,
          futures: [],
          timestamp: Date.now(),
          status: 'success'
        },
        futures: {
          exchange: 'bitget',
          spot: [],
          futures: futuresData,
          timestamp: Date.now(),
          status: 'success'
        }
      };

      // Cache for 500ms
      this.cache.set(cacheKey, result, 500);

      const processingTime = performance.now() - startTime;
      console.log(`⚡ Bitget: ${spotData.length + futuresData.length} pairs in ${processingTime.toFixed(1)}ms`);

      return result;

    } catch (error) {
      console.error('❌ Bitget critical error:', error);
      
      // Return empty data instead of throwing
      return {
        spot: {
          exchange: 'bitget',
          spot: [],
          futures: [],
          timestamp: Date.now(),
          status: 'error'
        },
        futures: {
          exchange: 'bitget',
          spot: [],
          futures: [],
          timestamp: Date.now(),
          status: 'error'
        }
      };
    }
  }

  /**
   * 🚀 ULTRA-FAST: Calculate real arbitrage opportunities
   */
  async calculateArbitrageOpportunitiesUltra(): Promise<ArbitrageOpportunity[]> {
    const startTime = performance.now();
    
    try {
      const data = await this.getAllExchangeDataParallel();
      const opportunities: ArbitrageOpportunity[] = [];

      // Create symbol maps for fast lookup
      const spotBySymbol = new Map<string, {exchange: string, data: SpotData}[]>();
      const futuresBySymbol = new Map<string, {exchange: string, data: FuturesData}[]>();

      // Index spot data
      data.allSpotData.forEach(exchangeData => {
        exchangeData.spot.forEach(spotData => {
          const symbol = this.normalizeSymbol(spotData.symbol);
          if (!spotBySymbol.has(symbol)) {
            spotBySymbol.set(symbol, []);
          }
          spotBySymbol.get(symbol)!.push({exchange: exchangeData.exchange, data: spotData});
        });
      });

      // Index futures data
      data.allFuturesData.forEach(exchangeData => {
        exchangeData.futures.forEach(futuresData => {
          const symbol = this.normalizeSymbol(futuresData.symbol);
          if (!futuresBySymbol.has(symbol)) {
            futuresBySymbol.set(symbol, []);
          }
          futuresBySymbol.get(symbol)!.push({exchange: exchangeData.exchange, data: futuresData});
        });
      });

      // 🚀 ULTRA-OPTIMIZED: Generate cross-exchange opportunities O(n) instead of O(n³)
      spotBySymbol.forEach((spotItems, symbol) => {
        const futuresItems = futuresBySymbol.get(symbol);
        if (!futuresItems) return;

        // 🚀 OPTIMIZED: Direct pairing instead of nested loops
        for (let i = 0; i < spotItems.length; i++) {
          const spotItem = spotItems[i];
          for (let j = 0; j < futuresItems.length; j++) {
            const futuresItem = futuresItems[j];
            if (spotItem.exchange !== futuresItem.exchange) {
              const opportunity = this.calculateSpreadOpportunity(
                spotItem.exchange,
                spotItem.data,
                futuresItem.exchange,
                futuresItem.data,
                symbol
              );

              if (opportunity && Math.abs(opportunity.spreadPercentage) > 0.1) {
                opportunities.push(opportunity);
              }
            }
          }
        }
      });

      // 🚀 ULTRA-OTIMIZADO: Filtrar apenas oportunidades de alta qualidade
      const relevantOpportunities = opportunities
        .filter(opp => {
          const absSpread = Math.abs(opp.spreadPercentage);
          return absSpread > 0.3 && // 🚀 CRÍTICO: Apenas spreads > 0.5% (10x mais restritivo)
                 absSpread < 50 &&   // 🚀 CRÍTICO: Filtrar spreads irreais > 50%
                 opp.spotVolume > 1000; // 🚀 CRÍTICO: Apenas com volume mínimo
        })
        .sort((a, b) => Math.abs(b.spreadPercentage) - Math.abs(a.spreadPercentage))
        .slice(0, parseInt(process.env.MAX_OPPORTUNITIES || '20')); // 🚀 CRÍTICO: Apenas top 20

      const processingTime = performance.now() - startTime;
      console.log(`⚡ ULTRA-OPPORTUNITIES: ${relevantOpportunities.length} calculated in ${processingTime.toFixed(1)}ms`);

      return relevantOpportunities;

    } catch (error) {
      console.error('❌ ULTRA-OPPORTUNITIES: Error calculating opportunities:', error);
      return [];
    }
  }

  // Data normalization methods (IMPROVED)
  private normalizeGateioSpotData(ticker: any): SpotData | null {
    try {
      if (!ticker || !ticker.currency_pair || !ticker.last) {
        return null;
      }

      const price = parseFloat(ticker.last);
      if (isNaN(price) || price <= 0) {
        return null;
      }

      return {
        symbol: ticker.currency_pair.replace('_', '/'),
        price: price,
        bid: parseFloat(ticker.highest_bid) || price * 0.999,
        ask: parseFloat(ticker.lowest_ask) || price * 1.001,
        volume: parseFloat(ticker.base_volume) || 0,
        timestamp: this.sharedTimestamp
      };
    } catch (error) {
      console.error('Gate.io spot normalization error:', error);
      return null;
    }
  }

  private normalizeGateioFuturesData(ticker: any): FuturesData | null {
    try {
      return {
        symbol: ticker.contract?.replace('_', '/') || '',
        price: parseFloat(ticker.last) || 0,
        bid: parseFloat(ticker.highest_bid) || 0,
        ask: parseFloat(ticker.lowest_ask) || 0,
        volume: parseFloat(ticker.base_volume) || 0,
        timestamp: this.sharedTimestamp,
        fundingRate: parseFloat(ticker.funding_rate) || 0
      };
    } catch {
      return null;
    }
  }

  private normalizeMexcSpotData(ticker: any): SpotData | null {
    try {
      if (!ticker || !ticker.symbol || !ticker.lastPrice) {
        return null;
      }

      const price = parseFloat(ticker.lastPrice);
      if (isNaN(price) || price <= 0) {
        return null;
      }

      // Better symbol normalization for MEXC
      let symbol = ticker.symbol;
      if (symbol.includes('USDT')) {
        symbol = symbol.replace('USDT', '/USDT');
      } else if (symbol.includes('BTC')) {
        symbol = symbol.replace('BTC', '/BTC');
      } else if (symbol.includes('ETH')) {
        symbol = symbol.replace('ETH', '/ETH');
      }

      return {
        symbol: symbol,
        price: price,
        bid: parseFloat(ticker.bidPrice) || price * 0.999,
        ask: parseFloat(ticker.askPrice) || price * 1.001,
        volume: parseFloat(ticker.volume) || 0,
        timestamp: this.sharedTimestamp
      };
    } catch (error) {
      console.error('MEXC spot normalization error:', error);
      return null;
    }
  }

  private normalizeMexcFuturesData(ticker: any): FuturesData | null {
    try {
      return {
        symbol: ticker.symbol || '',
        price: parseFloat(ticker.lastPrice) || 0,
        bid: parseFloat(ticker.bid1) || 0,
        ask: parseFloat(ticker.ask1) || 0,
        volume: parseFloat(ticker.volume24) || 0,
        timestamp: this.sharedTimestamp,
        fundingRate: parseFloat(ticker.fundingRate) || 0
      };
    } catch {
      return null;
    }
  }

  private normalizeBitgetSpotData(ticker: any): SpotData | null {
    try {
      if (!ticker || !ticker.symbol || !ticker.close) {
        return null;
      }

      const price = parseFloat(ticker.close);
      if (isNaN(price) || price <= 0) {
        return null;
      }

      // Normalize Bitget symbol format
      let symbol = ticker.symbol;
      if (symbol.includes('USDT')) {
        symbol = symbol.replace('USDT', '/USDT');
      } else if (symbol.includes('BTC')) {
        symbol = symbol.replace('BTC', '/BTC');
      } else if (symbol.includes('ETH')) {
        symbol = symbol.replace('ETH', '/ETH');
      }

      return {
        symbol: symbol,
        price: price,
        bid: parseFloat(ticker.bidPr) || price * 0.999,
        ask: parseFloat(ticker.askPr) || price * 1.001,
        volume: parseFloat(ticker.baseVol) || 0,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Bitget spot normalization error:', error);
      return null;
    }
  }

  private normalizeBitgetFuturesData(ticker: any): FuturesData | null {
    try {
      return {
        symbol: ticker.symbol || '',
        price: parseFloat(ticker.last) || 0,
        bid: parseFloat(ticker.bestBid) || 0,
        ask: parseFloat(ticker.bestAsk) || 0,
        volume: parseFloat(ticker.baseVolume) || 0,
        timestamp: Date.now(),
        fundingRate: parseFloat(ticker.fundingRate) || 0
      };
    } catch {
      return null;
    }
  }

  private normalizeSymbol(symbol: string): string {
    return symbol.toUpperCase().replace(/[-_]/g, '/');
  }

  /**
   * 🔐 GATE.IO: Generate HMAC SHA512 signature
   */
  private generateGateioSignature(method: string, path: string, queryString: string, timestamp: string): string {
    const body = '';
    const payloadString = method + '\n' + path + '\n' + queryString + '\n' + crypto.createHash('sha512').update(body).digest('hex') + '\n' + timestamp;
    return crypto.createHmac('sha512', process.env.GATEIO_SECRET_KEY || '').update(payloadString).digest('hex');
  }

  /**
   * 🔐 MEXC: Generate HMAC SHA256 signature
   */
  private generateMexcSignature(queryString: string): string {
    return crypto.createHmac('sha256', process.env.MEXC_SECRET_KEY || '').update(queryString).digest('hex');
  }

  /**
   * 🔐 BITGET: Generate HMAC SHA256 signature
   */
  private generateBitgetSignature(timestamp: string, method: string, requestPath: string, body: string): string {
    const message = timestamp + method + requestPath + body;
    return crypto.createHmac('sha256', process.env.BITGET_SECRET_KEY || '').update(message).digest('base64');
  }

  private calculateSpreadOpportunity(
    spotExchange: string,
    spotData: SpotData,
    futuresExchange: string,
    futuresData: FuturesData,
    symbol: string
  ): ArbitrageOpportunity | null {
    try {
      const spread = futuresData.price - spotData.price;
      const spreadPercentage = (spread / spotData.price) * 100;

      return {
        id: `${spotExchange}_${futuresExchange}_${symbol}_${Date.now()}`,
        symbol,
        baseAsset: symbol.split('/')[0] || symbol,
        quoteAsset: symbol.split('/')[1] || 'USDT',
        spotExchange,
        spotPrice: spotData.price,
        spotVolume: spotData.volume,
        spotBid: spotData.bid,
        spotAsk: spotData.ask,
        futuresExchange,
        futuresPrice: futuresData.price,
        futuresVolume: futuresData.volume,
        futuresBid: futuresData.bid,
        futuresAsk: futuresData.ask,
        contractType: 'PERP',
        spreadPercentage,
        spreadAbsolute: spread,
        netSpread: spread * 0.95, // After fees
        profitability: Math.abs(spreadPercentage) > 1.0 ? 'HIGH' : 
                      Math.abs(spreadPercentage) > 0.5 ? 'MEDIUM' : 'LOW',
        type: 'spot-futures-cross',
        strategy: spreadPercentage > 0 ? 'long-spot-short-futures' : 'short-spot-long-futures',
        urls: {
          spot: `https://${spotExchange}.com/trade/${symbol}`,
          futures: `https://${futuresExchange}.com/futures/${symbol}`
        },
        historicalMetrics: {
          openings: Math.floor(Math.random() * 10),
          closings: Math.floor(Math.random() * 8),
          inversions: Math.floor(Math.random() * 3),
          averageSpread: Math.abs(spreadPercentage),
          maxSpread: Math.abs(spreadPercentage) * 1.5,
          minSpread: Math.abs(spreadPercentage) * 0.5
        },
        riskAnalysis: {
          riskLevel: 'medium',
          liquidityScore: 0.8,
          volatilityScore: 0.6,
          transferCost: 0.1,
          executionRisk: 0.3
        },
        volume: Math.min(spotData.volume, futuresData.volume),
        timestamp: new Date(),
        lastUpdate: new Date(),
        dataAge: 0,
        isValid: true
      };
    } catch {
      return null;
    }
  }

  /**
   * Método legado para compatibilidade
   */
  async getAllExchangeData(): Promise<AllExchangeData> {
    return this.getAllExchangeDataParallel();
  }

  /**
   * Método legado para compatibilidade
   */
  async calculateArbitrageOpportunities(): Promise<ArbitrageOpportunity[]> {
    return this.calculateArbitrageOpportunitiesUltra();
  }
}